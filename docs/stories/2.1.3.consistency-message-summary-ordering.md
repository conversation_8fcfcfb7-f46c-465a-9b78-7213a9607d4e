# 2.1.3 Consistency：消息-摘要关联与顺序保证（P0）

Status: Planned

主题：对话流同步（一致性）

目标：确保“消息产生顺序、摘要更新顺序与 UI 展示顺序”一致；当出现乱序、重复或片段晚到时，系统进行去重/重排并维持摘要提示与消息定位的一致性，避免用户看到抖动或前后不一致的状态。

参考来源
- Epic（MVP）摘要与对话联动 [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md:60-66)
- PRD：对话交互与动态摘要（FR4/FR6） [`docs/prd.md`](docs/prd.md:74-83)
- 占位/错误一致性与可诊断基线 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)
- Reader 占位一致与测试基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35)

Acceptance Criteria
1) 顺序与去重
- 流式 token → 最终消息 → 摘要更新链路保证单调顺序：同一会话下，message.sequence 单调递增；客户端根据 sequence 进行稳定排序。
- 去重策略：基于 message.id 或 chunk.sequence 去重；重复到达不导致 UI 抖动或重复渲染。
- 乱序容忍：当晚到片段出现时，客户端缓冲窗口内（≤ N 条或 ≤ 1s）进行轻量重排，超过窗口按最终事件收敛。

2) 摘要“已更新”提示一致性
- 当某消息导致摘要变更时，右栏摘要 Tab 显示“已更新”徽标；点击摘要节点可定位到对应消息并高亮。
- 若最终收敛至另一条消息（重排后），摘要节点定位目标与高亮对象保持一致，且不出现错位。

3) UI 抖动与稳定性
- 对 Reader 段落与对话列表采用最小高度占位或过渡动画，避免大幅重排导致的体验抖动。
- 状态优先级遵循 Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady，不因重排打破。

4) 错误模型与诊断
- 若出现严重乱序（超出缓冲策略）或无法识别的重复，记录 console.warn 并携带 trace_id；不向用户暴露内部细节。
- 能在开发模式下输出一次性诊断：{ sessionId, lastSequence, arrivedSequence, trace_id }。

Dev Notes
- 建议后端写入 message.sequence，并以 DB 侧单调自增或服务层分配，避免仅靠客户端时间排序。
- 摘要与消息关联：在 end 事件或最终 JSON 中附带 caused_by_message_id，便于前端建立稳定映射。
- 轻量重排窗口：例如 1s 或 3 条消息内的重排，超过窗口则以最终事件 reconcile。
- 结合 2.1.1/2.1.2：SSE/重连场景下保证 sequence 与 caused_by_message_id 完整性。

Tasks / Subtasks
- [ ] 服务层：message.sequence 与 caused_by_message_id 输出（SSE end/JSON 一致）
- [ ] 前端：列表渲染稳定 key 与重排缓冲策略，重复去重
- [ ] 前端：摘要“已更新”徽标与消息定位的一致性处理
- [ ] 诊断：一次性 warn 与 trace_id 记录；开发下详细对象打印
- [ ] 测试：乱序/重复/晚到片段的重排与去重，摘要定位一致性

Testing
- 场景用例（Vitest + RTL）
  - 正常顺序：A→B→C，摘要对齐 C
  - 晚到：A→C（后到 B），在缓冲窗口内重排为 A→B→C，摘要定位仍指向 C
  - 重复：A→B→B（重复），UI 只渲染一条 B，不重复“已更新”
  - 超出窗口：产生 warn，最终以 end/最终 JSON 收敛为准

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）