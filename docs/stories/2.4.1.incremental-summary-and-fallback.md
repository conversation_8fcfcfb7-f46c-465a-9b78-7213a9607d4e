# 2.4.1 动态摘要：增量更新策略与截断回退（P1）

Status: Planned

主题：动态摘要优化

目标：为动态摘要引入“增量更新”策略，在对话流进行中逐步刷新摘要节点；当出现长回复或超时/中断时，提供“截断可感知”与“回退至上一版本”的安全机制；保证与消息-摘要关联的一致性与可诊断性。

参考来源
- PRD FR4/FR6：对话交互与动态摘要 [`docs/prd.md`](docs/prd.md:74-83)
- Epic（MVP）摘要与对话联动 [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md:60-66)
- 统一占位与 Reader 流程基线 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46), [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35)
- 一致性与顺序保证（消息-摘要关联）[`docs/stories/2.1.3.consistency-message-summary-ordering.md`](docs/stories/2.1.3.consistency-message-summary-ordering.md:1)

Acceptance Criteria
1) 增量摘要策略
- 在 SSE token 到达时按节拍合并为“增量摘要片段”，以 300–800ms 批次节流刷新 UI，避免抖动。
- 显示“摘要已更新”徽标；点击摘要节点定位到 caused_by_message_id 对应消息并高亮。

2) 长回复截断可感知
- 当回复长度预计超过策略阈值（例如 token 数量或时间阈值）时，摘要显示“部分已生成…”提示，提供“继续生成/展开”操作占位（后续故事可实现续写）。
- 截断发生时必须保持 UI 一致，不出现节点跳闪或布局大幅变化。

3) 回退至上一版本
- 若流式中断/超时/用户中止，摘要回退到上一稳定版本（latestVersion-1），并保留“尝试恢复/重试”入口。
- 回退后仍保留“已更新”徽标，但以弱态提示“已回退”。

4) 一致性与错误模型
- 摘要节点版本号单调递增；与消息 end 事件中的 caused_by_message_id 对齐。
- 错误占位与冲突提示互不干扰；错误态展示 trace_id 与重试 CTA（与 1.7/1.8 一致）。

Dev Notes
- 合并/节流策略：使用 rAF + setTimeout 组合批次提交；最大等待窗口≤800ms。
- 数据结构：summary.latest 与 summary.versions[]；UI 基于 latest 渲染，回退时引用上一版本。
- 可诊断：dev 环境在每次版本切换时 console.debug 输出 {version, causedBy, trace_id}。

Tasks / Subtasks
- [ ] UI：增量片段合并与节流刷新；“摘要已更新”徽标与定位
- [ ] 截断提示：长回复阈值与“继续生成/展开”占位
- [ ] 回退路径：中断/超时/取消的回退与重试入口
- [ ] 一致性：版本号与 caused_by_message_id 对齐
- [ ] 测试：增量刷新稳定性、截断提示、回退正确性、定位一致性

Testing
- Vitest + RTL
  - token 批次刷新不抖动；徽标出现；点击定位到对应消息
  - 长回复触发截断提示；无布局突变
  - 中断后回退上一版本；重试后恢复最新
  - 错误态 trace_id 显示与复制；重试成功路径

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）