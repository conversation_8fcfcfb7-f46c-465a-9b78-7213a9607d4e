# 1.11 Reader 与 API 超时与可中止请求贯穿（前端退避/超时 signal 落地与一致性 UI）

状态：Ready
类型：用户故事（前端范围；不修改后端契约）
承接：1.7 前端加载骨架与空状态统一规范；1.8 Reader 占位与测试补强；1.9 会话断点续学与状态持久化；1.10 前端真实数据状态收敛
来源：[`docs/prd.md`](docs/prd.md:1)、[`docs/architecture.md`](docs/architecture.md:1)、[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:1)、[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:1)

## 一、摘要

目标：在不修改后端契约的前提下，为前端 API 调用层与 Reader 页面贯穿“总超时 + 可中止请求（AbortController.signal）+ 指数退避”一致性能力，统一错误分类（network/timeout/unauthorized/notfound/server/unknown）与 UI 表达，避免“逻辑超时但网络请求未真正中止”的资源浪费与体验不一致问题。

范围：前端 apiClient 封装、Reader 页面接入、常量与文案补齐、单元测试与可访问性最小基线。

## 二、业务背景与动机

- PRD（NFR）要求性能与可靠性（P50 TTFB<1s、AI响应<3s、可用性≥99.5%），需在客户端实现稳健的超时与重试策略，防止卡顿与阻塞资源。
- 架构横切要求：客户端指数退避、服务端超时与有限重试、统一错误与 trace_id 贯穿（[`docs/architecture.md`](docs/architecture.md:69-74,92-95,205-215)）。
- 1.10 QA 明确建议：将 [`retryWithBackoff()`](ts.declaration:1) 的超时信号贯穿至 fetch（AbortController.signal），实现真正中止（[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:179-181,197-209)）。
- 1.9 的恢复与 Reader 状态机在长加载与错误场景需一致化体验与可测试闭环。

## 三、用户故事

作为阅读器用户，当网络缓慢或外部服务异常导致请求耗时过长时，我希望系统能在合理时间内终止请求并以统一的“仍在加载/已超时/可重试”方式反馈，同时保留错误分类和 trace_id，确保我可以安全地重试并尽快恢复学习。

## 四、验收标准（Acceptance Criteria）与验证结果

AC1 统一超时与中止贯穿
- 所有通过 apiClient 发起的请求均支持外部 AbortSignal；未提供时由“总体超时控制器”提供 signal 并传至底层 fetch。
- 超时或显式中止触发后，底层 fetch 被实际 abort；抛出的错误在归类后 type='timeout'。
验证：实现位于 [`retryWithBackoff()`](apps/frontend/src/api/client.ts:141) 与 Reader 层总控制器使用（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:236-243,372-376)）；单测覆盖“调用方传入 AbortSignal 能中止并被归类 timeout”（[`client.test.ts`](apps/frontend/src/api/client.test.ts:166-181)）。

AC2 退避策略与时间轴
- 指数退避：默认 base=500ms, max=3（尝试→延迟 500/1000/2000ms）；总超时（含重试与间隔）不超过 10s。
- 每次尝试均透传有效 AbortSignal；达总时限即停止后续尝试并抛出 timeout。
验证：退避与总时限逻辑已实现（[`client.ts`](apps/frontend/src/api/client.ts:141-232)），并通过单测验证网络重试与总时限 AbortError（[`client.test.ts`](apps/frontend/src/api/client.test.ts:122-147,149-165)）。

AC3 错误分类一致性
- TypeError 且无 status 视为 network；AbortError/超时归类为 timeout；HTTP 401→unauthorized，404→notfound，5xx→server，其余→unknown。
- 错误对象统一形态：{ type, messageKey, traceId?, original? }（UI 层优先从 ApiError.traceId/响应体 trace_id 映射）。
验证：归类函数已实现（[`classifyError()`](apps/frontend/src/api/client.ts:86-111)），Reader 捕获时按状态/Abort 区分并设置 UI（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:346-355)）；trace_id 透传已在 API/测试中验证（[`client.test.ts`](apps/frontend/src/api/client.test.ts:23-107)）。

AC4 Reader UI 与可访问性
- 加载超过 longLoadingMs 显示“仍在加载”提示（aria-live="polite"）；总超时/Abort 切换为错误态（type='timeout'）。
- 错误态显示统一文案与 Retry CTA；点击“重试”显式调用与首载一致的 load()，并创建新的控制器与 signal。
验证：长加载提示与 ErrorState 映射已在实现与组件中体现（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:399-407,411-424)，[`Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:53-75,128-168,170-181)），并通过页面测试验证“超时→重试→成功”（[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:323-380)）。

AC5 测试闭环
- [`client.test.ts`](apps/frontend/src/api/client.test.ts:1)：覆盖网络/超时/5xx 分类、500/1000/2000ms 退避、总超时中止与外部中止。
- [`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:1)：覆盖长加载→超时→重试→成功、手动取消→timeout、空态/摘要态；骨架最小时长。
- [`Placeholders.test.tsx`](apps/frontend/src/components/Placeholders.test.tsx:1)：校验 timeout 文案/CTA 与 a11y。
验证：上述测试均已补齐并通过。

## 五、范围内与范围外

范围内：  
- apiClient 退避/超时/AbortSignal 贯穿；错误分类稳定；Reader 接入与文案常量补齐；单测与 a11y 基线。

范围外：  
- 后端接口契约或错误模型变更；OpenAPI 更新；e2e 测试与离线缓存策略；全站统一中断总线（本故事聚焦 Reader 核心路径）。

## 六、设计与实现要点

1) API 层封装（不破坏对外签名）  
- 在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 增强：
  - fetchWithTimeout(fn, { totalTimeoutMs=10000, signal? }): 创建总体 AbortController，超时触发 abort，并将 signal 传给底层 fetch。  
  - 将 [`retryWithBackoff()`](ts.declaration:1) 调用改为向 fn 传入 signal（签名形如 fn(signal): Promise<T>），每次尝试可派生子控制器，保证局部中止与总中止均可触发。  
  - [`classifyError()`](ts.declaration:1) 增强对 AbortError/DOMException('AbortError') ⇒ timeout、TypeError 无 status ⇒ network、HTTP 401/404/5xx 映射；保留 ApiError 与 `_traceId` 透传。

2) 常量与文案  
- 在 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1) 新增：
  - errorTexts.timeout（title/subtitle/cta），longLoading 提示 copy 与 aria 文案 key。  
  - 继续复用 skeletonMinDurationMs、emptyMinViewMs 与 1.7/1.8 的既有键值。

3) Reader 接入  
- 在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)：
  - 首载 load()：创建总体控制器，传递 signal 至 api，启动加载计时；超过 longLoadingMs 显示“仍在加载”提示（aria-live）。  
  - 超时或 Abort：由 classifyError 返回 type='timeout'，渲染 ErrorState 并提供 onRetry。  
  - onRetry：取消旧控制器后，重新创建并调用与首载相同的 load()。

4) 组件一致性  
- 在 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1) 保持 ErrorState 的 data-testid 与 aria-live 基线，新增/对齐 errorType='timeout' 文案映射；不做视觉断言敏感改动，确保 1.8/1.10 测试稳定。

## 七、任务分解（面向研发）

T1 API 层：超时/中止贯穿  
- 调整 [`retryWithBackoff()`](ts.declaration:1) 的调用方式为向 fn 传递 signal；实现 fetchWithTimeout 并应用于所有请求路径。  
- 确保总超时≤10s；最后一次尝试超时能中止底层 fetch。

T2 错误分类增强  
- classifyError：AbortError/DOMException('AbortError') ⇒ timeout；TypeError 无 status ⇒ network；HTTP 状态映射；保留 traceId 归一化。  

T3 Reader 接入与长加载提示  
- 接入 longLoadingMs 与 totalTimeoutMs；超时→错误态（timeout）；onRetry→load()；保留最小骨架 300ms 与状态优先级。  

T4 文案与常量  
- 在 [`constants/ui.ts`](apps/frontend/src/constants/ui.ts:1) 增补 timeout 与 longLoading 文案 keys，并用于组件与页面。

T5 测试  
- [`client.test.ts`](apps/frontend/src/api/client.test.ts:1)：退避时间轴、总超时中止、超时/网络/5xx 分类断言（fake timers）。  
- [`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:1)：长加载→超时→重试成功、用户手动取消→timeout。  
- [`Placeholders.test.tsx`](apps/frontend/src/components/Placeholders.test.tsx:1)：timeout 文案/CTA 与 aria 校验。

## 八、依赖与风险

依赖：  
- 现有 API 契约稳定（[`docs/architecture.md`](docs/architecture.md:81-91)、[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:1-20)）。  
- 1.10 已有 [`classifyError()`](ts.declaration:1)/[`retryWithBackoff()`](ts.declaration:1) 初始实现（[`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)）。  

风险与缓解：  
- 环境差异（浏览器/Node）对 AbortError/TypeError 判定不一致 → 采用 name/是否存在 status 的组合规则，减少 message 依赖，并以单测覆盖。  
- 中止信号链路遗漏 → 统一由 apiClient 管理控制器；测试断言实际 abort 发生。

## 九、完成定义（DoD）

- 通过 AC1–AC5 单测；无新增 ESLint/TypeScript 错误；CI 通过。  
- 不破坏 1.7/1.8/1.9/1.10 的既有断言与体验；Reader 在长加载与超时场景下具备一致的 UI 和可重试路径；trace_id 可见性与可诊断性保持。

## 十、提交产物

- apiClient 超时/中止贯穿封装与单测  
- Reader 接入与单测  
- constants/ui.ts 文案键补齐与轻量文档更新

## 参考与证据

- 架构横切与错误模型/trace_id：[`docs/architecture.md`](docs/architecture.md:69-74,92-95,205-215,282-295)
- API 契约：[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:1-20)
- 前置故事与 QA 建议：
  - 1.7：[`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46,83-88,259-272)
  - 1.8：[`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,78-98,106-115)
  - 1.9：[`docs/stories/1.9.session-resume-and-state-persistence.md`](docs/stories/1.9.session-resume-and-state-persistence.md:24-35,66-86)
  - 1.10：[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:150-209)

---

## QA Results（最终复审追加）
结论：Approved（Ready）。统一“总超时 + Abort 可中止 + 指数退避 + 错误分类 + 一致性 UI”能力已贯穿 apiClient 与 Reader，测试证据充分，无阻断项。

一、对照 AC 的核查与证据
- AC1 统一超时与中止贯穿：外部 AbortSignal 与总体超时控制器皆能实际 abort fetch。实现与证据见 [`fetchWithTimeout()`](apps/frontend/src/api/client.ts:132-156)、[`retryWithBackoff()`](apps/frontend/src/api/client.ts:159-206)、[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:236-243,372-376)；单测覆盖外部中止归类 timeout（[`client.test.ts`](apps/frontend/src/api/client.test.ts:166-181)）。
- AC2 退避策略与时间轴：500/1000/2000ms 阶梯，≤10s 总时限，终止后停止后续尝试并抛出 timeout。实现与测试见 [`client.ts`](apps/frontend/src/api/client.ts:141-232) 与 [`client.test.ts`](apps/frontend/src/api/client.test.ts:122-165)。
- AC3 错误分类一致性：[`classifyError()`](apps/frontend/src/api/client.ts:86-111) 映射 network/timeout/unauthorized/notfound/server/unknown；Reader 捕获后统一 UI 与 traceId 显示（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:346-355)）。
- AC4 Reader UI 与 A11y：长加载提示 aria-live="polite"；超时切换错误态并显式 onRetry→load()（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:399-424)；[`Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:53-75,128-181)）。
- AC5 测试闭环：`client.test.ts` 覆盖网络/超时/5xx 分类、退避、总时限与外部中止；`Reader.test.tsx` 覆盖长加载→超时→重试→成功、空态/摘要态、骨架最小时长；`Placeholders.test.tsx` 校验 timeout 文案与 aria（见各文件路径引用）。

二、与 1.10/1.12 的衔接复核
- 能力对 1.10 的真实数据状态机与错误映射完全兼容，未引入后端契约变更。
- 对 1.12 的 sendMessage 流式路径提供同等总超时/Abort 能力，推荐配合 [`linkAbortSignals()`](apps/frontend/src/api/sse.ts:17-50) 与 [`parseSSEStream()`](apps/frontend/src/api/sse.ts:59-162) 复用。

三、遗留与建议（非阻断）
- classifyError 的 DOMException('AbortError') 与不同运行时的 message 兼容性已通过 name/status 组合判断缓解，但建议保留回归测试样例。
- 长加载提示的提示阈值与文案可集中 constants 配置，利于国际化与 A/B。

最终结论
- Approved（Ready）。可作为前端请求超时/中止/错误分类能力的统一基线。