# 1.3 Frontend：GET /api/sessions 与 Reader 原文加载/渲染（打通 Library → Reader → 学习会话闭环）

状态：Done
负责人：Frontend & Backend 协同
依赖：1.1 Project Skeleton & Session API、1.2 Frontend Create Session and Reader Handoff
目标落地时间：已完成

## 背景与目标

在 1.2 中，前端已支持从 Library 触发创建 Session 并跳转至 Reader。为了形成闭环，本故事将：
1) 在后端提供/完善 GET /api/sessions 接口以查询会话列表与详情；
2) 在前端 Library 页面加载并展示现有 Sessions；
3) 在 Reader 页面按 sessionId 拉取原文内容并渲染，支持基本的阅读交互（加载态、错误态、定位到首段）；
4) 确保 Library → Reader 的来回联动顺畅，形成最小可用学习闭环。

## 范围（In Scope）

- Backend
  - 提供 GET /api/sessions 列表查询与 GET /api/sessions/{id} 详情查询。
  - Session 详情响应中包含用于 Reader 的最小渲染所需字段：原文基本元数据与内容承载（可为占位/样本文本或已接通数据源）。
  - 补充必要的错误码与空列表语义。

- Frontend（apps/frontend）
  - Library：页面加载时调用 GET /api/sessions，展示 session 列表（标题/创建时间/状态），点击进入对应 Reader。
  - Reader：根据路由参数 sessionId 调用 GET /api/sessions/{id}，渲染原文标题与正文段落，包含加载与错误状态。
  - API client 扩展：在 `src/api/client.ts` 新增 sessions API 方法，统一错误处理。
  - 基础可用的 UI 状态（loading/empty/error）与测试数据可视验证。

- 合作对齐
  - OpenAPI 文档更新 docs/openapi.yaml 以反映新增/完善的响应结构（如与 1.1 已定义重复，以本故事定义为准同步校对）。

## 非范围（Out of Scope）

- 高级阅读交互（批注、划线、笔记、段落级引用）。
- 多分页/虚拟滚动性能优化。
- 鉴权/用户多租户（沿用现有后端默认策略）。
- 富文本样式设计（先以基础段落渲染为主）。

## 术语与数据契约

Session：一次学习会话实体，含基础元信息与与原文关联。  
Reader 原文：当前会话关联的可阅读文本内容集合（以段落数组呈现）。

### 后端 API 契约（以 FastAPI 为例）

1) GET /api/sessions  
查询会话列表（支持基础分页，先可不做分页，返回全部或限定上限）。

请求：
- Query 可选：`limit`（int，默认 50）、`offset`（int，默认 0）

响应 200：
```json
{
  "items": [
    {
      "id": "string-uuid",
      "title": "Article title",
      "status": "ready",
      "createdAt": "2025-08-06T07:00:00Z",
      "updatedAt": "2025-08-06T07:10:00Z"
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0
}
```

2) GET /api/sessions/{id}  
返回会话详情与用于 Reader 的最小内容。

响应 200：
```json
{
  "id": "string-uuid",
  "title": "Article title",
  "status": "ready",
  "createdAt": "2025-08-06T07:00:00Z",
  "content": {
    "language": "en",
    "source": "upload|url|sample",
    "paragraphs": [
      { "index": 0, "text": "First paragraph ..." },
      { "index": 1, "text": "Second paragraph ..." }
    ]
  }
}
```

错误：
- 404：找不到会话
- 500：服务器错误

注：如后端暂未接通真实原文，可返回示例 content 以便前端完成渲染与闭环验证。

## 前端改动点

文件位置（参考）：
- apps/frontend/src/api/client.ts
  - 新增 `getSessions()` 与 `getSessionById(id)`。
- apps/frontend/src/pages/Library.tsx（如尚无，建议新增；若 Library 由 App.tsx 承载则在其中实现列表区块）
  - 首屏加载 sessions，展示列表，点击跳转 `/reader/:sessionId`。
- apps/frontend/src/pages/Reader.tsx
  - 从 `useParams()` 获取 `sessionId`，调用 `getSessionById`，渲染标题与 paragraphs。
  - 加载态/错误态处理，空内容占位。

路由：
- 确保存在路由 `/reader/:sessionId` 至 Reader 页面
- Library 页面可为 `/` 或 `/library`

导航：
- Library 列表项点击 → navigate(`/reader/${id}`)；
- Reader 顶部提供返回 Library 的链接/按钮（可选）。

## 验收标准（Acceptance Criteria）

A1. 当用户访问 Library 页面时，系统调用 GET /api/sessions 并显示列表（若为空，显示“暂无会话”）。
A2. 点击任一列表项后跳转至 Reader，对应的 sessionId 能够在地址栏体现。
A3. Reader 页面在加载时显示 loading，成功后显示标题与正文段落；若 404，显示“会话不存在”错误；若 500，显示“加载失败，请稍后重试”。
A4. 返回 Library 后，仍可看到此前的列表（基本状态保持即可）。
A5. OpenAPI 文档同步包含上述两个接口的请求/响应定义且与实现一致。
A6. Lint 通过，前端构建成功；后端接口存在并可返回示例数据；手动走查闭环通过。
A7. 最少一条端到端演示路径：启动后端 → 启动前端 → 打开 Library → 看到 sessions 列表 → 点击一条进入 Reader → 看到原文渲染。

结论：上述 AC 已通过，闭环可演示，建议合并。

## 验证用例（Sample Scenarios）

- 用例 1：空列表
  - 后端返回 items=[]，前端显示“暂无会话”占位，无报错。
- 用例 2：正常数据
  - 列表显示 N 条，点入第 1 条，看到标题与至少 2 段文本。
- 用例 3：404
  - 直接访问一个不存在 id 的 reader 路由，显示“会话不存在”并提供返回 Library 的链接。
- 用例 4：错误重试
  - 后端短暂错误，前端显示错误并提供“重试”按钮（可选）。

## 技术实现要点

- API client 统一异常：将 fetch 异常、非 2xx 状态统一抛出带 code 的 Error，页面按状态分类提示；当返回并非 JSON 时，回退为文本提示（便于诊断），已在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts) 实施。
- Reader 渲染：
  - 仅做最小段落渲染，无需复杂样式；支持段落 key=paragraph.index。
  - 初次渲染滚动至顶部；拉取成功后设置 `document.title` 为会话标题，已在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx) 实施。
- 列表体验：
  - 显示相对时间，如“x 分钟前”，并保留本地时间用于核对，已在 [`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx) 实施。
- 性能与可靠性：
  - 列表加载添加最基本的 loading 文本。
  - 简单的请求取消/竞态避免（在 Reader 页面切换 id 时清理状态）。
- 分页与上限：
  - 后端对 limit 做封顶（50），在路由与 Service 层双重兜底，已在 [`apps/backend/app/api/routes.py`](apps/backend/app/api/routes.py) 与 [`apps/backend/app/services/sessions.py`](apps/backend/app/services/sessions.py) 实施；OpenAPI 已标注 maximum=50，并说明超出将被截断，详见 [`docs/openapi.yaml`](docs/openapi.yaml)。
- 示例内容说明：
  - 目前详情的 content.paragraphs 为“示例内容（sample）”，已在 OpenAPI 中明确，后续会替换为真实数据源。

## 拆分任务（Dev Tasks）

- [x] BE-1: 定义/对齐 Session 列表与详情 Pydantic 模型与路由，返回示例数据含 content.paragraphs。
- [x] BE-2: 更新 [`docs/openapi.yaml`](docs/openapi.yaml:1)，新增/校对 GET /api/sessions 与 /api/sessions/{id}。
- [x] FE-1: 扩展 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)，实现 `getSessions` 与 `getSessionById`，含错误处理。
- [x] FE-2: Library 页面渲染 sessions 列表（标题/时间/状态），点击跳转 Reader，已在 [`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1) 实施。
- [x] FE-3: Reader 页面基于 sessionId 拉取详情并渲染标题+段落，loading/error/empty 状态，已在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1) 实施。
- [x] FE-4: 路由与导航检查，确保从 Reader 可回到 Library（按钮或浏览器回退），已在 Reader 中提供按钮。
- [x] FE-5: 端到端走查与小结，闭环演示路径可达（本地服务已跑通）。

## 依赖与对齐

- 依赖 1.1 的后端项目骨架与 API 基础；依赖 1.2 的前端路由与 Reader 页面骨架。
- 如果现有后端已有 /api/sessions 的部分实现，需校对字段与响应结构；如不一致，以本故事定义为准进行统一。

## 风险与缓解

- 风险：后端暂无法提供真实原文内容。
  - 缓解：提供稳定的示例 content，前端完成闭环演示；后续再替换为真实数据源。
- 风险：字段命名/时间格式不一致导致前端渲染失败。
  - 缓解：以 OpenAPI 为单一事实源，严格对齐命名与格式，并在 PR 走查。

## 完成定义（DoD）

- 代码合并至主分支，CI 通过。
- OpenAPI 文档已更新且可被后端/前端引用（包含 limit 最大值与示例内容说明）。
- 本文故事文件合并，内容与实现一致，并附 PR 走查清单。
- 本地端到端演示成功，验证 A1–A7 全部通过。

## QA Results（仅追加评审结果）

结论
- 通过（Pass）。A1–A7 达成：列表/详情接口契约一致、Reader 能基于 sessionId 拉取并渲染原文，加载/空/错态具备，OpenAPI 与实现对齐；分页上限与 limit 截断按 50 的约束执行。

发现与证据
1) 列表与详情拉取
   - 前端列表使用 [`getSessions()`](apps/frontend/src/api/client.ts:521-533) 并透出 `_traceId`，Home 页在首次进入时调用并按 skeleton 最小时长切换状态（[`App.tsx`](apps/frontend/src/App.tsx:35-74,140-218)）。
   - 详情在 Reader 首载中通过 [`getSessionById()`](apps/frontend/src/api/client.ts:535-544) 拉取，错误分类与 traceId 显示透出（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:241-361)）。
   - 后端路由契约存在并与前端对齐：[`GET /api/sessions`](apps/backend/app/api/routes.py:77-136) 与 [`GET /api/sessions/{id}`](apps/backend/app/api/routes.py:138-192)。

2) UI 状态与最小占位
   - 列表：SkeletonList/EmptyState/ErrorState 接入，错误态包含 trace_id 与重试/反馈（[`App.tsx`](apps/frontend/src/App.tsx:170-187); [`Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)）。
   - Reader：SkeletonSessionHeader + SkeletonReader，错误态使用统一 ErrorState 并支持 retry→[`load()`](apps/frontend/src/pages/Reader.tsx:416-420,488-501)。

3) 时间与分页约束
   - 列表 limit 由服务端与 Service 层兜底为最大 50（[`routes.py`](apps/backend/app/api/routes.py:96-108)），文档 AC 一致。
   - 列表 UI 显示相对时间与本地时间戳（[`App.tsx`](apps/frontend/src/App.tsx:199-208)），符合“可视验证”。

4) 契约与错误模型一致性
   - apiClient 的 [`handleResponse()`](apps/frontend/src/api/client.ts:353-379) 透出 `_traceId`；错误走 `ApiError`，Reader/Library 能从 error.body/headers 取 trace_id 并展示，满足统一错误模型与可诊断性要求。
   - 后端错误响应均包装为统一 ErrorResponse，响应头回写 X-Trace-Id（[`routes.py`](apps/backend/app/api/routes.py:118-135,174-191)）。

5) 测试覆盖（代表性）
   - `client.test.ts` 覆盖 traceId 透传、退避/总时限中止、SSE/最终 JSON 两模式等（[`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1-409)）。
   - Reader 页面测试文件存在并覆盖骨架最小时长、错误/重试、长加载提示与摘要提示等路径（[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:1)）。

与 AC 的逐条结论
- A1 列表加载与空态：通过（Skeleton→Empty/Ready，trace_id 记录）。
- A2 跳转 `/reader/:id`：通过（[`App.tsx`](apps/frontend/src/App.tsx:208-215)，Reader 消费 useParams）。
- A3 Reader 加载态/错误态：通过（ErrorState 含 trace_id 与 retry, [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:488-501)）。
- A4 返回列表状态：通过（返回按钮/路由跳转，列表组件保持本地状态）。
- A5 OpenAPI 同步：现有 openapi.yaml 中已包含 sessions 列表/详情 schema；后端返回字段与前端消费一致。
- A6 Lint/构建与本地演示：通过（现有测试与构建脚本，列表与 Reader 验证路径可达）。
- A7 端到端演示路径：通过（Home→Reader→渲染段落）。

改进建议（非阻断）
- 列表错误日志当前在 Home 中输出一次 [ListError]，与此同时 ErrorState 渲染时也会输出诊断；建议保留组件侧日志、Home 侧降级为 debug 以避免重复日志（见 1.7 QA 建议，[`App.tsx`](apps/frontend/src/App.tsx:47-61)）。
- Reader 错误分类映射现依赖 status/name 推断，后续可直接复用 api 层 classifyError 的类型键，减少重复分类逻辑。