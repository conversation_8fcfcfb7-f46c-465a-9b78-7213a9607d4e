# 2.2.1 Reader 首载与懒加载骨架时序统一（P0）

Status: Planned

主题：骨架到真实数据收敛（前端）

目标：统一 Reader 首载与懒加载场景下的骨架显隐与状态优先级，避免闪烁与抖动；与站内统一占位规范一致，保障真实数据到达后的平滑过渡。

参考来源
- 统一占位规范与优先级 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46,109-125)
- Reader 占位与测试基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,106-115)
- 真实数据状态收敛策略（加载阈值/错误归类）[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:23-41,75-105)
- 常量与文案集中 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)

Acceptance Criteria
1) 骨架时序与最小显隐
- 首载与懒加载均显示 SkeletonReader；最小显隐时长 ≥ 300ms（skeletonMinDurationMs）。
- 当数据在 300ms 内返回，也保持骨架直到 300ms 再切换，避免闪烁。
- 懒加载（如切换 tab/子块）遵守相同阈值策略。

2) 状态优先级统一
- 渲染优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady。
- 错误→重试→成功路径中，确保在满足最小显隐后再切换，测试可观测。

3) 超时与弱提醒
- 加载超过 10s 显示“仍在加载…”弱提醒（非阻断），aria-busy=true，读屏文案友好。
- 仍在加载期间允许用户离开或重试关联业务流程。

4) 可观测与一致文案
- 骨架/空/错态文案与样式与 Library 一致；trace_id 透出遵循统一优先级（body.error.trace_id ＞ err.traceId ＞ URL ＞ uuid）。

Dev Notes
- 在 Reader.tsx 中增加“首帧起始时间”与“完成时间”，使用 setTimeout/Promise 协调最小显隐。
- 懒加载子区块（如摘要/消息）使用相同 hook 或辅助方法，减少重复。
- 注意与 2.2.2 的布局抖动治理协同（骨架占位高度与真实内容接近）。

Tasks / Subtasks
- [ ] 常量复用：skeletonMinDurationMs、emptyMinViewMs、loadingWeakNoticeMs(=10000)
- [ ] Reader：首载与子区块懒加载统一骨架时序
- [ ] 状态机：Error/Empty/Loading/Ready 优先级渲染
- [ ] A11y：aria-busy/aria-live，弱提醒读屏文本
- [ ] 测试：最小显隐、>10s 弱提醒、错误→重试→成功路径

Testing
- 用例（Vitest + RTL + fake timers）
  - 200ms 内返回：骨架仍保持至 300ms 后消失
  - 800ms 返回：骨架持续 ≥300ms，切换内容无闪烁
  - 超时 10s：出现弱提醒 aria 文本
  - 错误→重试→成功：遵守优先级，trace_id 可复制

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）