# 2.2.2 占位 → 真实数据过渡与闪烁/布局抖动治理（P0）

Status: Planned

主题：骨架到真实数据收敛（前端）

目标：在 Reader 与 Library 等关键页面中，将占位（Skeleton/Empty）平滑过渡到真实数据，最小化视觉闪烁与布局抖动（layout shift），统一占位块与真实内容的尺寸/节奏策略，确保体验与可测性一致。

参考来源
- 占位/空/错统一规范与最小显隐时长 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46,49-83,109-125)
- Reader 占位全面落地与测试基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,106-115)
- 真实数据状态收敛与错误归类/退避 [`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:23-41,75-116,121-129)
- UI 常量与文案集中 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)
- 组件占位集合与错误占位日志输出 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)

Acceptance Criteria
1) 尺寸映射与稳定高度
- Skeleton 段落块的行高/间距/最大宽度分布与真实段落接近（±10% 以内），切换时不产生明显 CLS（Cumulative Layout Shift）。
- 对列表与标题使用固定骨架高度（如标题行、摘要段落）以匹配真实组件占位。

2) 过渡动画与显隐策略
- 切换时采用最短 120–200ms 渐隐/渐显（prefers-reduced-motion=reduce 时禁用动画）。
- Skeleton → Content 过渡使用“交叉淡入”（cross-fade）而非“骨架收起 + 内容撑开”方式，降低位移感。
- 全局遵循 skeletonMinDurationMs（≥300ms），避免闪烁。

3) 懒加载与分页/增量渲染
- 对按需加载的块（如长列表分页或摘要片段），每块均使用与首屏一致的过渡策略；在同一屏内只显示一个状态（占位或内容），避免夹层状态。
- 列表增量插入使用“占位先行：固定高度 → 渐换真实内容”的方式，减少跳动。

4) 状态优先级与错误回退
- 优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady，与 2.2.1/1.7 保持一致。
- 从 Content 回退到 Error/Empty 时，采用轻量渐隐并稳定容器高度，避免突兀位移。

5) 可测性与诊断
- 为关键占位容器提供 data-testid（如 placeholder-reader, placeholder-list-item），测试可断言“切换无高度突变”的阈值。
- 在 dev 环境下，当检测到明显 CLS（可选阈值，例如 >0.25）时打印一次 warn（仅开发态）。

Dev Notes
- Skeleton 设计建议：
  - 段落骨架：高度约等于 1.2–1.4 行文字行高，宽度分布（100%、92%、88%、95%）循环，以接近真实文本断行。
  - 列表骨架：卡片高度固定，包含标题/副标题条形。
- 动画实现：
  - 使用 CSS prefers-reduced-motion 媒体查询；统一过渡变量（如 --placeholder-fade-ms）。
- 技术实现位置：
  - 占位组件放在 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)
  - 常量在 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)
  - Reader/列表容器在切换时机上遵守 2.2.1 的时序控制

Tasks / Subtasks
- [ ] 统一 Skeleton 尺寸映射：段落/标题/卡片的高度/间距/宽度分布
- [ ] Cross-fade 过渡：占位→内容 120–200ms；尊重 prefers-reduced-motion
- [ ] 列表与增量：占位先行固定高度→替换为真实卡片
- [ ] 回退路径：Content→Error/Empty 渐隐并保持容器高度稳定
- [ ] Dev warn（可选）：明显 CLS 时一次性 warn（仅开发态）
- [ ] 测试：data-testid 断言切换过程“高度不变或微小变动”，过渡时间窗口内快照稳定

Testing
- Vitest + RTL（必要时结合 jsdom 断言高度/样式）
  - Skeleton→Content：在 300ms 最小显隐后进行 120–200ms 交叉淡入，无明显高度跳变
  - 列表分页：新页占位替换真实卡片时不产生大幅位移
  - 错误回退：内容→错误占位切换平滑且高度近似稳定
  - A11y：prefers-reduced-motion 时无动画

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）