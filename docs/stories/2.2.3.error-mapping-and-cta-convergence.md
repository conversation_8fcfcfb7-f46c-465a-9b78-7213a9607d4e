# 2.2.3 错误归类与 CTA 收敛（P0）

Status: Planned

主题：骨架到真实数据收敛（前端）

目标：统一前端错误归类（network/timeout/unauthorized/notfound/server/unknown）与交互 CTA（重试/返回/新建/反馈），确保 trace_id 可见且可复制；Reader/Library/Session 三处一致，错误→重试→成功路径稳定可测。

参考来源
- PRD/NFR 与统一错误模型 [`docs/prd.md`](docs/prd.md:85-95,235-248)
- 架构：接口与横切（追踪/性能/退避）[`docs/architecture.md`](docs/architecture.md:79-96,205-216,282-301)
- 统一占位与 Reader 落地/测试基线 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46,83-107,109-125), [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,106-115)
- 真实数据一致性与错误映射建议 [`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:41-66,104-116,137-156)
- 客户端重试/退避测试覆盖 [`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1-409)
- 占位组件与错误日志输出 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)

Acceptance Criteria
1) 错误归类与文案
- 将错误归类为：network, timeout, unauthorized, notfound, server, unknown。
- constants/ui.ts 提供 messageKey 与 CTA 映射；UI 基于 key 渲染文案与按钮（不写死文案，便于 i18n）。
- trace_id 文案统一“问题追踪ID：{trace_id}”，支持一键复制。

2) CTA 策略
- network/timeout：主 CTA=重试（onRetry），次 CTA=离线提示/反馈。
- unauthorized：主 CTA=返回首页/重新创建会话（依据上下文）；可选“重新登录”占位。
- notfound：主 CTA=返回首页，次 CTA=新建会话。
- server/unknown：主 CTA=重试，次 CTA=反馈（mailto 或 issue 模版）。

3) trace_id 归一化优先级
- 优先级：body.error.trace_id ＞ err.traceId（客户端包装）＞ 响应头 X-Trace-Id ＞ URL query ＞ 随机 uuid。
- ErrorState 渲染时 console.info 输出 { page, trace_id, path, retryable }，与 1.7/1.8 一致。

4) 退避与总时限协同
- 页面侧重试按钮触发时，走与首次相同的 load()（或 send()）函数，内部包含 retryWithBackoff（指数退避）与总时限（10s）策略。
- 多次失败后仍停留错误态，继续允许手动重试。

5) 三处一致性
- Library 列表、Session 详情、Reader 页面错误归类/文案/CTA 一致；a11y: aria-live="assertive"。
- 与状态优先级一致：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady。

Dev Notes
- classifyError(error): { type, messageKey, retryable, original }
- mapErrorToCtas(type, context): { primary, secondary? }；context 影响“返回首页/新建会话”的选择
- Placeholders.ErrorState 可接受 errorType 与 onRetry、onReport、primaryAction/secondaryAction props

Tasks / Subtasks
- [ ] API 层：classifyError 归类标准化（保持对外签名兼容）
- [ ] UI 常量：constants/ui.ts 增加错误文案/CTA 字典
- [ ] 组件：Placeholders.ErrorState 扩展 props，支持 CTA 映射与 trace_id 复制
- [ ] 页面：Reader/Library/Session 接入统一错误渲染与 onRetry=load()
- [ ] 日志：渲染错误占位时 console.info 输出诊断上下文
- [ ] 测试：错误映射→文案/CTA 渲染，重试路径恢复成功

Testing
- Vitest + RTL
  - network/timeout：断言“重试”触发 load()；错误多次后仍可手动重试
  - unauthorized/notfound：断言返回首页/新建 CTA 存在
  - server/unknown：断言“反馈”存在；trace_id 显示与复制
  - trace_id 归一化优先级满足预期（优先取 body.error.trace_id）
  - 三处页面一致性：组件渲染断言与 a11y 属性

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）