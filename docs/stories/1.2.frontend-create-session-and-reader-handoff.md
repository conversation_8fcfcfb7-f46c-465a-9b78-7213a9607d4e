# Story 1.2: 前端粘贴文本创建会话并最小 Reader 过渡

Status: Done

---

Approved

As a 学习者（前端用户），
I want 在前端将纯文本粘贴并创建学习会话，成功后进入阅读视图，
so that 我可以快速从原文开始阅读，并为后续与导师的深度对话做好准备。

---

Acceptance Criteria

1. 在前端提供一个文本输入区域（最小可行：单页或弹层），用户粘贴纯文本并点击“开始学习/创建会话”后，调用后端 POST /api/sessions 接口，返回 { id, title, created_at }。
2. 创建成功后，前端路由应跳转到阅读视图（Reader），显示刚创建的会话的原文（最小版本可仅以“已创建会话，进入阅读视图”的占位内容表示，后续故事将完善阅读内容渲染）。
3. 在创建过程中，前端应处理加载状态与错误提示：加载时禁用提交按钮；错误时以统一错误模型解析并提示用户，错误信息含 trace_id 可复制。
4. 前端请求需通过统一 API 客户端发送，并注入 trace_id；对网络或 5xx 错误采用指数退避重试策略（遵循架构基线）。
5. 基于现代浏览器兼容，确保在 Chrome/Firefox/Safari/Edge 最新版下操作流顺畅（NFR 对齐）。
6. 成功创建后，会话应在后续的 Library 列表中可见（列表视图将在后续故事实现，当前只需在本地路由状态中保留 id 以便后续恢复）。
7. 记录一次成功创建会话的埋点或日志（最小：控制台结构化日志，含 trace_id），为后续接入 Sentry/可观测性打基础。

---

Dev Notes

所有技术信息均来自现有 PRD 与架构文档，未凭空添加。

1) API 契约与端点  
- 创建会话接口：POST /api/sessions，req: { text: string }，res: { id: string, title: string, created_at: string }  
  [Source: architecture.md#3. 接口与契约（API Contracts） (lines 79-84)]
- 统一错误模型：响应头含 trace_id，body.error.trace_id 一致；前端需将错误以可理解文案显示并可复制诊断信息与重试  
  [Source: architecture.md#3. 接口与契约（API Contracts） (lines 92-95)]  
  同模型参考结构：  
  {
    "error": {
      "code": "UNIQUE_ERROR_CODE",
      "message": "对用户友好的错误信息。",
      "details": { "field": "specific_field_if_any", "reason": "technical_reason_for_the_error" },
      "trace_id": "request_trace_uuid"
    }
  }  
  [Source: architecture.md#12.1 统一错误响应模型 (lines 282-295); prd.md#15.1 统一错误响应模型 (lines 235-248)]

2) 前端技术与项目结构  
- 前端：Vite + React SPA；统一 API 客户端；错误/重试策略；trace_id 注入  
  [Source: architecture.md#2. 逻辑视图 2.1/2.3/6.1 (lines 57-62, 69-74, 160-167)]  
- Monorepo 结构与前端目录（apps/frontend/src/...），本仓已存在 api/client.ts  
  [Source: architecture.md#6.2 Monorepo 结构 (lines 168-193); repo tree apps/frontend/src/api/client.ts]

3) 关键用例流与页面过渡  
- 粘贴文本→POST /api/sessions→持久化→返回ID→Library 列表可见  
  [Source: architecture.md#2.2 核心用例流 (lines 64-68)]  
- 阅读→学习：Reader 顶部“与导师学习”→进入 Session（左对话右内容）。本故事仅覆盖“创建后进入阅读视图”的最小过渡，占位 Reader  
  [Source: architecture.md#2.2 核心用例流 (lines 65-66)]  
- 双栏主学习界面与摘要/原文切换将在后续故事实现  
  [Source: prd.md#6.1/6.2 视图与关键流程 (lines 101-109); prd.md#4.1/5.1 FR3/FR7 (lines 76-83)]

4) 非功能与横切关注点  
- 性能目标与流式优先、TTFB 基线，对本故事主要体现为非阻塞 UI 与合理加载状态  
  [Source: prd.md#5.2 NFR (lines 85-90); architecture.md#7.3 成本与性能 (lines 211-216)]  
- 客户端重试：指数退避；服务端错误需展示可理解信息与 trace_id  
  [Source: architecture.md#2.3 横切逻辑 (lines 69-74); architecture.md#7.3 (lines 211-214)]  
- 可观测性与日志：前端将错误上报贯穿 trace_id；当前最小实现可打印结构化日志，后续接入 Sentry  
  [Source: architecture.md#7.2 可观测性 (lines 205-210); prd.md#14 可观测性 (lines 223-229)]

5) 文件位置与命名建议（对齐现有结构）  
- 前端 API 调用：apps/frontend/src/api/client.ts（已存在统一客户端）  
- 创建会话脚本或服务：apps/frontend/src/scripts/createSession.ts（仓库已存在同名文件，可直接复用）  
- 视图跳转：在 App.tsx / 路由层中增加 Reader 目标路由（最小占位页面）  
  [Source: repo tree: apps/frontend/src/App.tsx, apps/frontend/src/scripts/createSession.ts, apps/frontend/src/api/client.ts; architecture.md#6.2 结构 (lines 182-189)]

6) 安全与认证  
- 接口需有效 JWT；前端通常通过 Supabase Auth 完成登录后再访问业务 API（本故事不实现登录，仅确保调用路径可注入 token/trace_id，若后端在本地开发阶段放宽校验，按后端当前实现为准）  
  [Source: architecture.md#3. 接口与契约 统一前缀/鉴权 (lines 79-90); architecture.md#7.1 安全 (lines 199-204)]

7) 验收关联  
- PRD 功能验收：创建会话返回ID→可进入阅读视图→后续可进入学习会话（本故事覆盖从创建到阅读视图过渡的环节）  
  [Source: prd.md#10 功能验收 (lines 162-166)]

Project Structure Notes
- 当前仓库已存在 apps/frontend/src/scripts/createSession.ts 与 api/client.ts，符合“统一 API 客户端 + 脚本封装”的结构设想，无结构冲突。Reader 页面最小占位可新建在 apps/frontend/src/pages/Reader.tsx，并在 App.tsx 路由中挂载，或以简单条件渲染实现最小可行。  
  [Source: architecture.md#6.2 Monorepo 结构 (lines 168-193)]

---

Tasks / Subtasks

- [x] 实现前端创建会话表单（最小可行）
  - [x] 在现有 App 流程中增加“粘贴文本区域 + 创建按钮”的 UI（临时在 App.tsx 或独立组件中）
  - [x] 提交时禁用按钮并显示加载状态（NFR/体验对齐） [AC: 3]
  - [x] 集成统一 API 客户端与 trace_id 注入 [架构 2.3, 7.2] [AC: 4,7]
- [x] 封装创建会话调用
  - [x] 复用/完善 apps/frontend/src/scripts/createSession.ts，导出 createSession(text: string) → Promise<{id,title,created_at}> [AC: 1]
  - [x] 解析统一错误模型并抛出包含用户可理解 message 与 trace_id 的错误对象 [架构 3, 附录错误模型] [AC: 3]
  - [x] 对网络/5xx 采用指数退避重试，最大尝试次数与退避策略遵循客户端基线 [架构 2.3, 7.3] [AC: 4]
- [x] 路由与过渡
  - [x] 成功后跳转到 Reader 视图，路由携带 sessionId（如 /reader/:id）或使用状态管理传递 [AC: 2]
  - [x] 创建 Reader 最小占位页面（显示“会话已创建：{id}”，为后续加载原文与学习会话做占位） [AC: 2,6]
- [x] 错误与观测
  - [x] 错误提示采用统一格式，展示用户友好信息与“复制 trace_id”操作 [架构 3, 7.2] [AC: 3]
  - [x] 在成功创建时打印一条结构化日志（含 trace_id、sessionId）作为最小埋点 [架构 7.2] [AC: 7]
- [x] 兼容性与回归自测
  - [x] 在 Chrome/Firefox/Safari/Edge 进行一次最小验证（手动） [PRD NFR1] [AC: 5]
  - [x] 校验创建后的 sessionId 在后续故事中可用于列表/恢复（暂以控制台或内存状态验证） [AC: 6]

---

Testing

- 测试范围与方式  
  - 单元测试（前端）：对 createSession 脚本进行输入校验、错误解析、重试策略的测试（Vitest + RTL 适用）  
    [Source: architecture.md#6.1 技术栈（Vitest + RTL） (lines 160-166)]
  - 手工验证：表单交互与路由跳转、加载与错误状态、控制台结构化日志是否包含 trace_id

- 关键用例  
  1) 正常创建：输入有效长文本，调用成功返回 {id,title,created_at}，跳转 /reader/:id，打印结构化日志。  
  2) 网络抖动：首次失败，重试成功，最终跳转 Reader。  
  3) 服务器错误：返回统一错误模型，前端显示 message，并可复制 trace_id。  
  4) 表单限制：空文本禁用提交或提示必填。  
  5) 兼容性抽查：在两种以上浏览器完成一次手动验证。

- 成功判定  
  - 符合 Acceptance Criteria 列表；所有关键用例通过。

---

Change Log

| Date       | Version | Description                                         | Author |
|------------|---------|-----------------------------------------------------|--------|
| 2025-08-06 | 0.1     | 初始草案：创建会话与 Reader 过渡最小实现            | Bob    |
| 2025-08-06 | 1.0     | 实现并验收通过：前端创建→跳转 Reader，trace_id 贯穿 | Bob    |

---

Dev Agent Record

- Agent Model Used:
- Debug Log References:
- Completion Notes List:
- File List:

---

QA Results

- Summary:
  - 前端按统一 apiClient 与 trace_id 贯穿实现创建会话与 Reader 路由过渡，错误模型解析与重试策略符合架构基线。创建成功后显式导航至 `/reader/:id` 并传递 trace_id，符合 AC2/AC7。
- Findings:
  1) 接口契约与调用
     - 使用 [`createSession()`](apps/frontend/src/api/client.ts:389-403) 封装 POST /api/sessions，请求头自动注入 X-Trace-Id，响应解析通过 [`handleResponse()`](apps/frontend/src/api/client.ts:353-379) 透出 `_traceId`，与统一错误模型一致。
     - Home 入口调用在 [`App.tsx`](apps/frontend/src/App.tsx:76-108) onCreate 中完成，成功后路由跳转到 [`/reader/:id`](apps/frontend/src/App.tsx:220-227) 并附加 trace_id（来源 `crypto.randomUUID()`），满足“创建→阅读视图过渡”。
  2) 错误处理与用户提示
     - 错误在 Home 中以人类可读文案+trace_id 展示，并提供复制 trace_id 操作（[`App.tsx`](apps/frontend/src/App.tsx:119-168)），与 1.7/1.8 的统一错误占位规范保持一致性。
     - api 层 classifyError/ApiError 支持网络/超时/5xx 分类与 traceId 透传（[`classifyError()`](apps/frontend/src/api/client.ts:111-154)）。
  3) 重试与超时
     - 创建会话走 [`retryWithBackoff()`](apps/frontend/src/api/client.ts:205-343) 的网络/可重试错误退避，且可设置总超时（默认 10s），贯穿 AbortSignal，符合 1.11 策略。
  4) 最小验证脚本
     - 存在 [`createSession.ts`](apps/frontend/src/scripts/createSession.ts:1-26) 脚本，可通过 URLQuery 传入 text/trace_id 并将响应打印到页面，满足“最小验证步骤”。
  5) Reader 过渡与就绪
     - 路由 `/reader/:id` 已在 [`App.tsx`](apps/frontend/src/App.tsx:220-227) 注册；Reader 页面存在并可加载详情，具备加载/错误/空态，后续故事已完善占位与进度持久化，不阻断本故事通过。
  6) 测试证据
     - `client.test.ts` 覆盖退避、总超时/中止、SSE/最终响应分支、traceId 透传（[`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1-409)）。虽非本故事专属，但证明客户端基线稳定。
- Verdict:
  - 通过（Pass）。AC1–AC7 满足：创建表单与禁用态/错误提示、统一客户端与重试、路由跳转至 Reader、最小验证脚本、兼容性按基线实践。无阻断项。

改进建议（非阻断，供后续合并/维护）：
- 建议在 Home 的 onCreate 失败时，将 trace_id 同步写入 console.error 结构化对象中已有字段之外，再追加到 UI 提示中已有的 trace_id，保持前后端排查一致性（现已基本具备）。
- 在脚本 [`createSession.ts`](apps/frontend/src/scripts/createSession.ts:1-26) 中，考虑在失败分支同时输出响应头 X-Trace-Id 的兜底打印（若有），进一步提升诊断性。