# 2.4.2 动态摘要：缓存与命中提示（P1）

Status: Planned

主题：动态摘要优化

目标：在摘要生成/读取路径中引入轻量缓存与命中指示（Hit Indicator），当命中最近版本或等价摘要时，以非打扰方式提示用户“摘要已更新于 T/已命中缓存”，并保持与消息-摘要关联一致与可诊断性。

参考来源
- PRD FR6：动态摘要最小可用与更新提示 [`docs/prd.md`](docs/prd.md:76-83)
- Epic（MVP）摘要与对话联动 [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md:60-66)
- Reader 一致性与可测试性基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35)
- 消息-摘要关联与顺序一致性 [`docs/stories/2.1.3.consistency-message-summary-ordering.md`](docs/stories/2.1.3.consistency-message-summary-ordering.md:1)

Acceptance Criteria
1) 缓存策略与等价判定
- 定义轻量缓存键：sessionId + summarySpec（如最新版本/视图参数）；缓存值含 { version, updatedAt, contentHash }。
- 等价判定：当 version 相同或 contentHash 一致时视为命中；contentHash 可为摘要文本的稳定哈希（忽略空白差异）。
- 缓存新鲜度：默认 5 分钟新鲜期（可配置），超期即刻刷新且不显示命中提示。

2) 命中提示与定位
- 命中缓存时，在摘要 Tab 显示弱提示：“已命中缓存（更新于 T）”；不阻断交互。
- 点击“查看来源”可定位到最后一次触发该摘要的消息（caused_by_message_id），并高亮对话条目。

3) 同步与回退协同
- 若 2.4.1 触发回退（latestVersion-1），缓存应同步回退为上一版本，提示“已回退（更新于 T）”。
- 当新的消息到达并触发摘要更新后，命中提示自动移除或更新为最新时间点。

4) 错误与空态
- 缓存读取/写入失败时静默降级（warn 一次）；不影响摘要展示。
- 当无摘要时不显示命中提示，显示空态占位（与 1.7/1.8 一致）。

Dev Notes
- 缓存介质：前端内存 + 可选 localStorage（按 sessionId 隔离）；注意大小上限与清理策略。
- Hash 建议：稳定化处理（trim、归一化换行等）后使用短哈希（如 murmur/xxhash 前端实现）。
- 可诊断：dev 模式打印 { cacheKey, hit: boolean, version, trace_id }。

Tasks / Subtasks
- [ ] 缓存层：getSummaryCache/setSummaryCache/clearSummaryCache
- [ ] 命中提示：Tab 弱提示与“查看来源”定位
- [ ] 回退协同：回退时同步缓存并更新提示文案
- [ ] 诊断：一次性 warn 与 dev 调试信息
- [ ] 测试：命中/未命中、过期、回退协同、定位一致性

Testing
- Vitest + RTL
  - 命中缓存显示弱提示；点击定位到消息
  - 过期后刷新：提示消失或更新时间更新
  - 回退：缓存回到上一版本，提示“已回退”
  - 空态：无摘要不显示提示；错误路径静默

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）