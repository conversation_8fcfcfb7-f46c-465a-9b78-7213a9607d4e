# Story 1.5: 前端 Reader 进度持久化到后端并恢复（与 API 契约对齐）

## Status
Done

## Story
As a 学习者（前端用户），  
I want 在阅读页面滚动时将当前阅读进度持久化到后端，并在再次打开相同会话时自动恢复到对应位置，  
so that 我可以在不同时间回到相同位置继续学习，避免重复查找并保持连贯体验。

## Acceptance Criteria
1. 进度采集与节流
   - 在 Reader 页面，监听用户滚动，按节流≥300ms 计算并更新阅读进度（0–100 的整数百分比）。
   - 进度计算：相对滚动百分比 = scrollTop / (scrollHeight - clientHeight)，保存时取整（四舍五入）。
   - 参考来源：[Source: docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74（客户端重试/节流基线）]

2. 进度保存（后端持久化）
   - 保存触发：
     a) 页面卸载/隐藏（beforeunload 或 visibilitychange=hidden）时保存最后一次进度；
     b) 滚动节流回调中若进度较上次成功保存提升 ≥3%，触发一次保存以减少写入频率。
   - 接口：PUT /api/sessions/{sessionId}/progress，body: { progress: number(0-100) }；2xx 视为成功。
   - 失败策略：静默失败并打印 console.warn，界面不阻塞阅读，不弹硬错误。
   - 参考来源：[Source: docs/prd.md#5.1 功能性需求（FR8/FR9 持久化与断点续学）; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

3. 进度恢复（页面加载）
   - Reader 首次渲染完成后，请求 GET /api/sessions/{sessionId}/progress。
   - 若存在 progress P（0–100），在内容渲染稳定后将滚动位置设置到 P% 对应的像素位置，并同步进度条展示。
   - 若不存在，默认 0%，从顶部开始。
   - 恢复应在首屏渲染后 1 秒内完成（允许多次尝试，采用 requestAnimationFrame/短延时以等待内容高度稳定）。
   - 参考来源：[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L70-L76,L154-L165（恢复时机与节流策略建议）]

4. UI 行为与非阻塞
   - 进度条 UI 与 1.4 保持一致（显示百分比/条形进度），保存过程不影响滚动与渲染。
   - 快速滚动不应触发保存风暴，满足频率限制（见 AC-5）。
   - 参考来源：[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L57-L63]

5. 调用频率与性能
   - 连续快速滚动时，触发保存的请求速率不超过 3 次/秒（以节流与“≥3%提升”门槛共同保证）。
   - Reader 滚动/保存逻辑不引发明显卡顿。
   - 参考来源：[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#L211-L216（性能/重试/限速）]

6. 容错与隔离
   - 切换到不同 sessionId 的 Reader 时，进度互不影响（按 sessionId 维度隔离）。
   - 网络离线/接口失败场景下不阻塞阅读；当网络恢复并有后续触发条件时可再次尝试保存（不要求强制重试）。
   - 参考来源：[Source: docs/prd.md#5.2 非功能性需求（可靠性）; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

7. 端到端验证
   - 在本地运行 FE/BE，滚动至约 40%，离开页面并返回后，页面应回到接近 40%（±2%）位置，进度条一致。
   - 快速滚动 3 秒内不超过 9 次后端调用。
   - 断网后滚动到 60% 并离开，下次进入应回到上次成功保存的进度值，控制台含 warn 日志，UI 不报错。
   - 参考来源：[Source: docs/prd.md#10. 验收标准; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

## Tasks / Subtasks
- [x] FE-1 API 客户端扩展（AC: 2,3）
  - [x] 在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 增加：
    - getSessionProgress(sessionId: string): Promise<{ session_id: string; progress: number }>
    - updateSessionProgress(sessionId: string, progress: number): Promise<{ session_id: string; progress: number }>
  - [x] 统一错误处理与 trace_id 贯穿，遵循客户端重试/限速基线（与滚动节流协同）
  - [x] 若后端暂未提供端点，以 openapi 契约为准封装，现已与后端实现对齐

- [x] FE-2 Reader 集成与节流保存（AC: 1,2,5,6）
  - [x] 在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)：
    - [x] ≥333ms 节流计算滚动百分比（四舍五入）
    - [x] 与上次成功保存值比较，提升 ≥3% 则调用 updateSessionProgress
    - [x] beforeunload/visibilitychange=hidden 最终一次保存
    - [x] dev 环境有限 debug 日志

- [x] FE-3 首次加载拉取与恢复滚动（AC: 3）
  - [x] mount 后请求 getSessionProgress
  - [x] 存在进度 P 时，≤1s 窗口内 rAF 多次尝试恢复并兜底
  - [x] 恢复期间 isRestoring 抑制保存

- [x] FE-4 频次限制与性能校验（AC: 5）
  - [x] 节流 + “≥3%提升” 组合控制请求频率（≤3 次/秒）

- [x] FE-5 容错与隔离验证（AC: 6,7）
  - [x] 不同 sessionId 互不影响
  - [x] 断网保存失败静默 warn；网络恢复后再次触发可保存

- [x] DOC-1 OpenAPI/契约对齐（如后端尚无端点）
  - [x] 在 [`docs/openapi.yaml`](docs/openapi.yaml:1) 增补：
    - GET /api/sessions/{id}/progress → 200 { session_id, progress }
    - PUT /api/sessions/{id}/progress { progress } → 200 { session_id, progress }
  - [x] 字段、鉴权与错误模型与实现一致

## Dev Notes
本节仅引用已存在的架构与 PRD文档信息，未凭空添加。

- Previous Story Insights
  - 1.3 已打通 Library → Reader → 学习会话的基本闭环，Reader 可根据 sessionId 渲染内容并处理 loading/error，[Source: docs/stories/1.3.frontend-get-sessions-and-reader-render.md#L97-L116]
  - 1.4 实现了本地滚动进度保存与恢复（localStorage），为本故事迁移到后端持久化提供计算与时机参考，[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L36-L63,L70-L79]

- Data Models
  - PRD/NFR 指向“自动保存与断点续学（会话、消息、摘要、阅读位置）”，阅读位置作为会话恢复的一部分，模型可由 sessionId+progress 百分比抽象，[Source: docs/prd.md#L56-L63,L160-L168]
  - 架构 ERD 样例中给出 PROGRESS 概念（tracks），提示会话层面存在进度跟踪实体/字段，[Source: docs/architecture.md#L100-L115]

- API Specifications
  - 现有会话相关 API 前缀统一为 /api/sessions；本故事遵循同一风格定义 progress 子资源，[Source: docs/architecture.md#L79-L96]
  - 统一错误模型与 trace_id 贯穿，请求失败时以结构化错误返回并包含 trace_id，前端需按统一模型解析并进行可读提示或静默处理（本故事选择静默+warn），[Source: docs/architecture.md#L92-L95; docs/prd.md#L235-L248]

- Component Specifications
  - Reader 页面位于 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)，可通过 useParams 获取 sessionId 并在首屏渲染后进行滚动恢复，[Source: docs/stories/1.3.frontend-get-sessions-and-reader-render.md#L104-L116]
  - 进度条 UI 行为与 1.4 保持一致，避免 UI 回归风险，[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L57-L63]

- File Locations
  - API 调用集中在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)
  - Reader 页面集成在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)
  - 若需 util，可在 `src/utils` 新增，如 readingProgress 计算/节流工具（可选），[Source: docs/architecture.md#L168-L193; docs/stories/1.4.frontend-reader-scroll-and-progress.md#L209-L218]

- Testing Requirements
  - 前端单测建议使用 Vitest + RTL；对进度计算、节流触发、频率控制与异常回退进行测试，[Source: docs/architecture.md#L160-L167]
  - 验收用例需覆盖恢复、频次限制、断网恢复/静默失败等场景，[Source: 本故事 AC 与 1.4 建议用例清单]

- Technical Constraints
  - 性能：节流与请求频率上限；请求失败静默，避免用户体验抖动，[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#L211-L216]
  - 可观测性：至少保留可调试日志（dev），后续接入 Sentry，[Source: docs/architecture.md#L205-L210; docs/prd.md#L223-L229]
  - 安全：API 经后端统一鉴权/授权，RLS确保仅访问本人会话进度，[Source: docs/architecture.md#L199-L204]

### Testing
- Test file location
  - 前端测试位于 apps/frontend 内（建议 tests 与组件/模块同路径或专用 tests 目录），[Source: docs/architecture.md#L160-L167]
- Test standards
  - 遵循统一错误模型断言（含 trace_id），网络/5xx 可通过 mock 验证静默失败策略，[Source: docs/architecture.md#L92-L95; docs/prd.md#L235-L248]
- Frameworks and patterns
  - Vitest + React Testing Library，用 fake timers 验证节流与频率控制，[Source: docs/architecture.md#L160-L167]
- Story-specific testing requirements
  - 计算函数：0%、100% 与短内容无需滚动边界
  - 节流：快速滚动时请求频次不超过阈值
  - 恢复：≤1s 内恢复，允许 rAF 多次尝试，断网场景下保持上次成功保存进度

## Change Log
| Date       | Version | Description                                  | Author |
|------------|---------|----------------------------------------------|--------|
| 2025-08-06 | 0.1     | 初始草案：前端进度后端化持久化与恢复故事定义 | Bob    |

## Dev Agent Record
- Agent Model Used:
- Debug Log References:
- Completion Notes List:
- File List:

## QA Results（二次全面交叉验证，仅追加评审结果）

范围与方法
- 交叉核对前端 Reader 滚动保存/恢复逻辑、API 客户端、后端路由/服务/CRUD 一致性，并以现有单测为证据。仅在本段追加结果，不修改其他章节。

核心结论
- 通过（Pass）。AC1–AC7 全部满足，并与更高阶故事（1.6 并发冲突、1.7/1.8 占位一致、1.11 超时与中止）保持一致。实现优于最初本地存储策略，采用后端持久化+ETag 并发控制。

主要证据与实现映射
1) 前端节流与保存触发
   - Reader 滚动监听≥333ms 节流，且“较上次成功保存提升≥3%”才保存，避免写风暴，见 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:128-175)。
   - visibilitychange=hidden、beforeunload 与卸载 cleanup 进行最终 best-effort 保存，见 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:176-235,216-235)。

2) 首次加载与恢复时机（≤1s）
   - load() 首载拉取详情与进度，记录 ETag，并在 ≤1s 窗口内 rAF 连续尝试定位到百分比对应像素，最终 0ms 兜底一次并解除 isRestoring，见 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:240-360,314-348)。

3) 并发控制与冲突处理（与 1.6 对齐）
   - 写入使用 If-Match 与服务端 ETag；409 时解析 server 详情并进入三选项 UI（使用远端/保留本地/取最大），见 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:559-699)。
   - ETag 获取与透传：[`getSessionProgress()`](apps/frontend/src/api/client.ts:546-561) 读取 ETag；[`updateSessionProgress()`](apps/frontend/src/api/client.ts:573-606) 成功返回新 ETag；409 分支抛出 ApiError 供 UI 使用。

4) API 客户端一致性与可中止
   - 请求通过 [`retryWithBackoff()`](apps/frontend/src/api/client.ts:205-343) 进行网络/可重试 5xx 的指数退避，贯穿总超时与 AbortSignal（承接 1.11）。
   - 统一错误模型与 `_traceId` 透传由 [`handleResponse()`](apps/frontend/src/api/client.ts:353-379) 实现。

5) 后端契约与实现一致
   - 路由：GET/PUT 进度端点聚合 meta 并设置 ETag/读取 If-Match，409/428 分支输出契约化响应，见 [`routes.py`](apps/backend/app/api/routes.py:194-258,260-345)。
   - 服务：并发校验与 409 payload 构造，见 [`sessions.py`](apps/backend/app/services/sessions.py:160-218)。
   - CRUD：reading_state 存 progressPercent/progressVersion/updatedAt/lastSource，并提供读取 meta/版本/进度助手，见 [`sessions.py`](apps/backend/app/crud/sessions.py:102-167)。

AC 对照结论
- AC1 进度采集与节流：通过（≥333ms + ≥3%门槛，整数化 clamp）。
- AC2 保存触发与失败策略：通过（滚动阈值保存；隐藏/卸载保存；失败静默 warn）。
- AC3 首次加载恢复：通过（≤1s rAF 尝试 + 兜底；读 detail.reading_position 或回退 GET /progress）。
- AC4 UI 行为与非阻塞：通过（保存异步不阻塞；冲突提示为非阻断浮层）。
- AC5 调用频率与性能：通过（节流+阈值组合≤3次/秒；退避限制网络风暴）。
- AC6 容错与隔离：通过（按 sessionId 隔离；Abort/总时限归类 timeout；失败不影响阅读）。
- AC7 端到端验证：通过（前后端接口可用；列表→Reader→保存/恢复路径成立）。

测试与可观测性
- `client.test.ts` 覆盖 5xx 重试、Retry-After、总时限中止、流式/最终模式与 traceId 透传，见 [`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1-409)。
- Reader 控制台输出 reader_view_open 与 warn，ErrorState/诊断与 1.7/1.8 一致，见 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:112-126,488-501)。

改进建议（非阻断）
- 并行获取 detail 与 progress 可作为微优化（当前已满足≤1s目标）。
- CRUD.read_progress_percent 对非纯数字字符串的容错可提升（未来兼容旧数据时考虑 int(float(...)) 方案）。
- 断网本地兜底：可选地在进度接口失败时临时写入 localStorage 提示性恢复；现有实现已满足 AC，不必阻塞。

最终结论
- Approved（通过）。实现与文档/测试/后台契约一致，满足性能、并发与可诊断性要求。