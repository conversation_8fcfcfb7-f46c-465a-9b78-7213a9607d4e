# 2.3.3 冲突三决策与滚动平滑回放（P1）

Status: Planned

主题：跨设备继续阅读

目标：在检测到阅读进度冲突（参见 2.3.1）时，提供三种明确的用户决策路径——“使用远端”“保留本地”“合并（取最大）”，并在选择后以平滑滚动回放至决定的进度位置；过程不阻断阅读、可诊断、可测试，与 1.6 的 ETag/If-Match 并发控制对齐。

参考来源
- 并发控制与 409 冲突交互（GET ETag / PUT If-Match / 409 回退）[`docs/stories/1.6.cross-device-session-sync-and-resume.md`](docs/stories/1.6.cross-device-session-sync-and-resume.md:100-141,167-189)
- 占位/错误统一与诊断（冲突提示与错误占位独立）[`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)
- Reader 占位、重试与可测试性基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,106-115)
- 真实数据一致性与错误映射（前端仅消费现有 API）[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:23-41,50-66)

Acceptance Criteria
1) 三决策 UI 与默认项
- 非阻断提示浮层或轻量面板，包含三按钮：
  - 使用远端（remoteOffset）
  - 保留本地（localOffset）
  - 合并（max(remoteOffset, localOffset)）
- 默认项由 2.3.2 的规则计算，高亮呈现；所有按钮具备清晰文案与说明。
- 操作后关闭浮层，并以平滑滚动滚动到目标进度（±容差）并更新本地基线。

2) ETag/If-Match 并发闭环
- 执行决策后，调用 PUT /api/sessions/{id}/progress：
  - If-Match 携带最新版本（若选择“使用远端”，则先同步最新 ETag；“保留本地/合并最大”亦需获取或持有最新版本）。
- 若仍返回 409：
  - 解析 server.version/server.offset；允许再次进入三决策 UI（递归 1 次），或提示“已更新为服务端最新”，避免递归风暴。
- 成功后更新 ETag、本地缓存与 UI 基线。

3) 平滑滚动与容差窗口
- 滚动定位时使用 requestAnimationFrame/短延时进行 1–2 次对齐尝试，目标区域容差 ±1%（或 ±一个段落高度），避免因高度差异导致偏移。
- 恢复完成后解除 isRestoring 标志，恢复正常节流保存与阈值判断。

4) 诊断与 a11y
- 选择时打印一次 console.info：{ choice, localOffset, remoteOffset, appliedOffset, trace_id }。
- 浮层可通过键盘操作与读屏可达；按钮 aria-label 明确；默认项 aria-selected。

5) 与错误占位互不干扰
- 冲突提示独立于错误占位：当请求错误（非 409）时进入 ErrorState；409 则进入冲突 UI；两者不互相覆盖。

Dev Notes
- 冲突 UI 组件建议：ConflictResolutionDialog(props)；与 Reader 状态机解耦。
- 选择与回放过程需与进度节流保存逻辑协同：isRestoring=true 时暂停写入，结束后恢复。
- 防抖：避免在短时间内重复弹出冲突 UI（可依据 minute 阈值/ETag 更新判断）。

Tasks / Subtasks
- [ ] 组件：ConflictResolutionDialog（默认项高亮、三按钮、a11y）
- [ ] API：获取最新 ETag、PUT If-Match、处理 409 重入一次
- [ ] 滚动：平滑滚动与容差窗口，对齐 1–2 次尝试
- [ ] 状态：isRestoring 标志与节流保存协同
- [ ] 诊断：console.info 选择与结果日志；trace_id 透出
- [ ] 测试：三决策路径、409→重试成功、滚动容差达标、不与错误占位冲突

Testing
- Vitest + RTL
  - 默认项：根据 2.3.2 规则断言预选/高亮
  - 使用远端/保留本地/合并：三路成功路径，PUT 成功后更新 ETag
  - 409 再入：最多 1 次重入，最终成功或提示“采用服务端最新”
  - 滚动容差：最终位置在目标 ±1%/±一个段落高度
  - a11y：ESC 关闭、Tab 顺序、aria-selected 与 role 属性

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）