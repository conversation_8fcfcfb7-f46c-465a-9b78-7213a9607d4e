# 2.5.1 空/错误态统一深化：状态矩阵回归与测试套件（P1）

Status: Planned

主题：空/错误态统一

目标：在 Library/Session/Reader 三处完成“加载/空/错误/内容”状态矩阵的回归核查与测试套件补强，统一 trace_id 展示与复制、错误归类与 CTA 行为、a11y 属性与最小显隐时长，确保在真实数据与网络条件下表现一致、可诊断、可自动化回归。

参考来源
- 统一占位规范与文案/交互基线 [`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46,83-125)
- Reader 侧全面落地与测试补强基线 [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35,106-115)
- 真实数据状态收敛与错误映射（不改后端契约）[`docs/stories/1.10.frontend-real-data-states-convergence.md`](docs/stories/1.10.frontend-real-data-states-convergence.md:23-66,121-156)
- 错误归类与 CTA 收敛（P0 子题完成后回归）[`docs/stories/2.2.3.error-mapping-and-cta-convergence.md`](docs/stories/2.2.3.error-mapping-and-cta-convergence.md:1)

Acceptance Criteria
1) 状态矩阵覆盖（3 页面 x 4 状态）
- Library / Session / Reader 三处均支持：LoadingSkeleton、Empty、Error、Content。
- 最小显隐时长：骨架 ≥300ms（skeletonMinDurationMs）；空态展示最小 300ms（emptyMinViewMs），避免闪烁。
- 状态优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady（统一判定与实现）。

2) 错误归类与 CTA 一致
- 错误类型映射为 network/timeout/unauthorized/notfound/server/unknown，并据此渲染 CTA（重试/返回/新建/反馈）。
- trace_id 归一化优先级：body.error.trace_id ＞ err.traceId ＞ X-Trace-Id ＞ URL query ＞ uuid；ErrorState 支持复制 trace_id。
- 渲染错误占位时 console.info 输出 { page, trace_id, path, retryable }。

3) a11y 与可达性
- 占位根节点设置 role 与 aria-live（错误态 assertive，其他 polite）；按钮 aria-label 清晰。
- 焦点顺序正确，错误/空态出现时不丢失焦点；复制 trace_id 后给予反馈（读屏/视觉）。

4) 真实数据路径回归
- 在真实 API 条件（可通过 mock/spy 替代网络）下验证：列表空、详情空、Reader paragraphs=[]、5xx/断网、超时等场景的渲染与 CTA 行为。
- 与 1.6 的冲突提示互不干扰：409 进入冲突 UI，不进入错误占位；非 409 错误进入 ErrorState。

5) 稳定与可测性
- 测试中通过 data-testid 与语义选择器进行断言，不依赖像素；对时间相关行为使用 fake timers 与辅助推进（advanceAndFlush）。
- 错误日志具备基本节流（可选：按 traceId/timewindow 降低重复输出；非必须，作为信息性建议）。

Dev Notes
- UI 常量集中：[`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)
- 占位组件：[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)
- 页面容器：Library（App/Library.tsx）、Session 详情（若独立容器则对齐此故事）、Reader（[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)）
- 对接 2.2.3 的 classifyError 与 CTA 映射，避免页面各自实现导致漂移

Tasks / Subtasks
- [ ] 常量与组件复核：skeletonMinDurationMs / emptyMinViewMs / errorTexts / emptyTexts
- [ ] 三处页面接入统一占位并核对状态优先级
- [ ] 错误归类接入与 CTA 行为统一（onRetry=load()；返回/新建/反馈）
- [ ] a11y 校验：aria-live/role/aria-label 与焦点管理
- [ ] 诊断与日志：ErrorState 渲染时 console.info 输出上下文
- [ ] 测试套件：加载/空/错/内容状态切换；trace_id 展示与复制；重试恢复路径；真实数据下 notfound/timeout/server/unknown 等映射

Testing
- 组件层（Placeholders.test.tsx）：错误/空/骨架渲染、CTA/复制、aria 属性
- 页面层（Reader.test.tsx + Library/Session 页面测试）：
  - 骨架最小显隐≥300ms
  - 空态最小展示≥300ms
  - 错误态 trace_id 展示/复制，onRetry=load() 后恢复内容态
  - notfound/unauthorized/server/unknown 文案与 CTA 映射正确
  - 与 1.6 冲突提示共存：409 → 冲突 UI；非 409 → 错误占位

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）