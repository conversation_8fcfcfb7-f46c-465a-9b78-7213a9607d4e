# 2.1.1 Backend：消息写入一致性与流式接口稳健化（P0）

Status: Planned

主题：对话流同步（后端）

目标：提升 POST /api/sessions/{id}/messages 的稳定性与一致性，确保流式（SSE）与最终 JSON 回落一致、超时与有限重试策略明确、幂等键支持、统一错误模型贯穿，以支撑前端对话状态机与自动重连。

参考来源
- PRD：功能与非功能约束 [`docs/prd.md`](docs/prd.md:71-95,85-95)
- 架构：接口契约与横切关注点 [`docs/architecture.md`](docs/architecture.md:79-96,199-216,282-301)
- Epic1 故事：超时与可中止、流式与最终路径 [`docs/stories/1.11.reader-timeout-and-abortable-requests.md`](docs/stories/1.11.reader-timeout-and-abortable-requests.md:1)
- 现有代码：后端 LLM 服务与消息编排 [`apps/backend/app/services/llm.py`](apps/backend/app/services/llm.py:1), [`apps/backend/app/services/messages.py`](apps/backend/app/services/messages.py:1)

Acceptance Criteria
1) 接口契约与路径
- 提供 POST /api/sessions/{id}/messages，支持两种响应模式：
  - SSE 流式：Content-Type: text/event-stream；事件边界与 UTF-8 分片安全，结束事件包含 trace_id 与最终消息 meta。
  - 最终 JSON 回落：当 caller 指定 &mode=final 或 SSE 不可用时，返回 { messageId, parts[], usage, trace_id }。
- 两种模式在内容语义、错误语义与 trace_id 一致。 
- 契约在 [`docs/openapi.yaml`](docs/openapi.yaml:1) 中同步（若尚未存在对应条目则新增/对齐）。

2) 超时与有限重试
- 外部 LLM 请求：统一总时限（默认 10s，可配置），对 5xx/网络类错误采用指数退避（3 次阶梯，如 500/1000/2000ms），遵循成本与性能基线。
- 服务端对流式写入提供保底 flush，并在超过总时限时发送终止事件与统一错误模型。
- 客户端可通过 AbortSignal 中止请求；服务端接收取消信号后尽快结束流并写入日志。

3) 幂等与一致性
- 支持 Idempotency-Key（请求头或 query），在短时间窗口内（默认 5 分钟）相同 Key 返回相同 messageId/结果。
- 消息顺序保证：写入采用单事务或“保存-标记完成”的两段式，避免乱序；重复片段去重。

4) 统一错误模型
- 任意失败场景返回统一错误结构，含 trace_id；SSE 模式通过 error 事件与最终终止事件携带相同 trace_id。
- X-Trace-Id 响应头与 body.error.trace_id 保持一致。

5) 观测与日志
- 结构化日志包含：sessionId、messageId、idempotencyKey、provider、延迟、重试次数、trace_id。
- 错误率、P99 延迟指标可见并接入告警（Sentry/日志聚合）。

Dev Notes
- 服务层建议：messages.send_and_persist(session_id, prompt, options) → streaming iterator | final result
- LLM 适配层：抽象 provider 客户端，统一超时/重试/错误分类
- 事件定义：SSE 事件类型建议为 token, usage, end, error；end 携带 messageId 与 trace_id
- 数据一致性：持久化先写“进行中”记录，落地成功后置完成状态；失败清理/补偿策略记录

Tasks / Subtasks
- [ ] OpenAPI 对齐：POST /api/sessions/{id}/messages（SSE 与 JSON 模式）
- [ ] Service：send_and_persist 实现与事务策略、重复/乱序去重
- [ ] LLM 调用器：统一超时/重试策略与错误分类
- [ ] SSE 输出：事件边界/编码/flush 与终止事件一致性
- [ ] 幂等键支持：存储短期映射（key → messageId/result）
- [ ] 测试：集成测覆盖超时/重试/Abort/幂等/错误模型/trace_id 一致性

Testing
- 后端集成（Pytest + httpx TestClient）
  - 正常流式：断言事件序列与 end 事件 trace_id 存在
  - 回落 JSON：mode=final 返回完整消息与使用量
  - 超时：流式中止并输出统一错误事件，最终终止事件一致
  - 幂等：相同 Idempotency-Key 返回相同 messageId
  - 错误模型：5xx/网络异常统一结构输出，头/体 trace_id 一致

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）
