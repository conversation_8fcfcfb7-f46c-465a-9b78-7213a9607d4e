# 2.3.1 阈值 Gating：冲突提示判定函数与单测（P1）

Status: Planned

主题：跨设备继续阅读

目标：抽象并落地“是否提示阅读进度冲突”的判定函数（shouldPromptConflict），以时间窗口与进度差异双阈值进行 gating；补齐单元测试矩阵，与 1.6 的并发控制与 1.5 的进度持久化一致。

参考来源
- 跨设备同步与恢复（需要阈值 gating 函数与单测）[`docs/stories/1.6.cross-device-session-sync-and-resume.md`](docs/stories/1.6.cross-device-session-sync-and-resume.md:175-189)
- 进度持久化（阈值与总时限基线）[`docs/stories/1.5.frontend-reader-progress-persist.md`](docs/stories/1.5.frontend-reader-progress-persist.md:37-52,73-80)
- 统一占位/错误模型/trace_id（用于冲突 UI 与诊断独立）[`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)

Acceptance Criteria
1) 判定函数与默认阈值
- 导出 shouldPromptConflict(remoteUpdatedAt, localUpdatedAt, remoteOffset, localOffset, opts?)
  - opts = { minute=5, delta=100 }（默认值可配置）
- 返回布尔值：当“时间接近（|remote-local| ≤ minute）且 offset 差异 ≥ delta”时返回 true；否则 false。

2) 单元测试矩阵
- 时间更近/更远、差异大/小、边界相等（±1 分钟、差值=100）等组合覆盖。
- 时间不接近但差异很大 → false（仅提示在“疑似并发写”的窗口内）。
- 兼容时间字符串与 Date 实例；非法输入兜底为 false 并记录 warn（仅开发态）。

3) 与前端冲突 UI 对接（占位）
- Reader 初始化阶段调用 shouldPromptConflict 做 gating；为 2.3.3 的三决策 UI 提供前置布尔判定。
- 不改变现有错误占位/重试逻辑，冲突提示独立。

4) 文档化与可配置
- constants/ui.ts 增加 conflictGating 默认阈值；导出类型与注释便于复用。
- README/CHANGELOG 可选记录。

Dev Notes
- 时间比较采用毫秒差；minute=5 → 5*60*1000 ms。
- offset 可与 1.5 的百分比/像素映射保持一致，统一使用“抽象 offset number”。

Tasks / Subtasks
- [ ] 实现 shouldPromptConflict 与类型定义
- [ ] constants/ui.ts 增加默认阈值与导出
- [ ] 单测：边界/正常/非法输入全覆盖
- [ ] 接入 Reader 初始化（占位调用），不改现有 UI

Testing
- Vitest
  - 用例：近/远、差值=99/100/101、±5 分钟边界；非法日期/NaN offset 兜底
  - 断言：返回布尔一致；warn 调用次数（开发态）

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）