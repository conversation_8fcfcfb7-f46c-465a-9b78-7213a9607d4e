# 1.9 会话断点续学与状态持久化（读写恢复闭环收束）

Status: Approved

## Story
作为一名回到学习任务的用户，
我希望在关闭页面或中断后再次进入能够自动恢复上次的对话、摘要与阅读位置，
从而无缝继续深度学习，不需要重复操作和回忆上下文。

## Acceptance Criteria
1) 数据契约与接口
   - GET /api/sessions/:id 返回 { id, title, created_at, messages[], summary?, reading_position? } 满足架构契约。
   - 前端 Reader/Session 容器在首载时根据该接口一次性恢复 UI 状态（消息、摘要、阅读位置）。
   - 错误返回遵循统一错误模型，包含 trace_id；前端正确显示错误占位并提供重试。

2) 阅读位置恢复
   - 记录并持久化用户的阅读位置（例如段落索引或滚动偏移）至后端或适当持久层。
   - 再次进入阅读视图时自动定位到该位置；切换至学习会话再返回阅读视图保持一致。

3) 对话与摘要恢复
   - 再次进入学习会话视图，左栏对话完整展示历史消息，右栏摘要展示最近版本或为空。
   - 摘要存在版本化（追加式），读取时使用最新版本（version 最大值）。

4) 统一占位与错误处理
   - 加载数据时显示骨架，最小显示时长 300ms，避免闪烁；空列表/无摘要时显示空态。
   - 错误态显示 trace_id，可复制；提供显式重试 load()；console.info 输出诊断上下文（page/path/trace_id/retryable）。

5) 一致性约束
   - 遵循状态优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady。
   - 前端使用 constants/ui.ts 的 skeletonMinDurationMs、emptyMinViewMs、errorTexts/emptyTexts。

6) 测试覆盖
   - 单测覆盖：阅读位置恢复、消息/摘要恢复、错误态 trace_id 显示与复制、retry→load() 成功路径、首次加载骨架最小显隐。
   - 使用 fake timers 并实现 advanceAndFlush，推进渲染帧与清空微/宏任务，规避竞态。

## Dev Notes

本故事信息严格来源于架构与 PRD，禁止发明新规范或技术栈外元素；若架构未给出则显式声明“无具体指引”。

- 数据模型与接口对齐
  - Session/Message/Summary 共享类型：[Source: architecture.md#12.2 前后端共享类型](docs/architecture.md:297-301)
  - 会话详情接口契约（包含 messages/summary/reading_position）：[Source: architecture.md#3. 接口与契约](docs/architecture.md:81-91)
  - 统一错误模型与 trace_id 贯穿：[Source: architecture.md#3. 接口与契约-统一错误模型](docs/architecture.md:92-95), [Source: architecture.md#12.1 统一错误响应模型](docs/architecture.md:282-295)

- 逻辑与恢复流程
  - 关键用例流：断点续学（GET /api/sessions/{id} 恢复消息、摘要、阅读位置与 UI 偏好）：[Source: architecture.md#2.2 核心用例流](docs/architecture.md:64-68)
  - 追踪与重试：前端客户端负责 trace_id 注入、指数退避重试；服务端外部调用设定超时与有限重试：[Source: architecture.md#2.3 横切逻辑](docs/architecture.md:69-74)

- 状态管理与占位一致性
  - 占位/错误/空态规范承接上一故事统一规则（最小骨架时长、状态优先级、可复制 trace_id、console.info 诊断）：[Source: docs/stories/1.8.reader-unified-placeholders-and-tests.md#Acceptance Criteria](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35), [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#业务规则与统一规范](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)
  - 前端项目结构放置（pages/components/constants）：[Source: architecture.md#6.2 Monorepo 结构](docs/architecture.md:168-193)

- 安全与可观测
  - 不泄露内部错误详情，仅展示 trace_id；日志/指标/追踪要求：[Source: architecture.md#7.2 可观测性](docs/architecture.md:205-210)
  - 客户端重试、服务端超时与速率限制基线：[Source: architecture.md#7.3 成本与性能](docs/architecture.md:211-215)

- 测试策略
  - FE 测试栈：Vitest + RTL：[Source: architecture.md#6.1 技术栈](docs/architecture.md:160-167)
  - 假时钟推进、错误→重试路径断言方法（承接 1.7/1.8 测试规范）：[Source: docs/stories/1.8.reader-unified-placeholders-and-tests.md#Testing](docs/stories/1.8.reader-unified-placeholders-and-tests.md:106-115)

若某分类信息在架构缺失：
- 本地阅读位置编码方式（像素/段落索引）的具体字段名：No specific guidance found in architecture docs
- 前端状态库选择（Zustand/Redux）的具体实现：No specific guidance found in architecture docs

### File Locations
- 前端页面容器：apps/frontend/src/pages/Reader.tsx [Source: apps/frontend/src/pages/Reader.tsx](apps/frontend/src/pages/Reader.tsx:1)
- UI 常量：apps/frontend/src/constants/ui.ts [Source: apps/frontend/src/constants/ui.ts](apps/frontend/src/constants/ui.ts:1)
- 占位组件：apps/frontend/src/components/Placeholders.tsx [Source: apps/frontend/src/components/Placeholders.tsx](apps/frontend/src/components/Placeholders.tsx:1)
- API 客户端：apps/frontend/src/api/client.ts [Source: apps/frontend/src/api/client.ts](apps/frontend/src/api/client.ts:1)

## Tasks / Subtasks
[x] 后端契约核验与补齐（AC: 1,3）
  [x] 确认 GET /api/sessions/:id 返回 messages、summary（最新版本）、reading_position 字段 [Source: docs/architecture.md#3](docs/architecture.md:81-91)
  [x] 如缺失 reading_position：在 schema 与服务层补齐（读取/更新）并对齐 ERD 的 reading_state/active_chunk_index [Source: docs/architecture.md#4.1 ERD](docs/architecture.md:100-115)
[x] 前端 Reader 首载恢复（AC: 1,2,3,5）
  [x] 在 Reader.tsx 初始化阶段调用 api 获取会话详情，归一化 trace_id [Source: docs/architecture.md:92-95]
  [x] 根据 reading_position 恢复滚动/段落定位；若无则回退到顶部或上一已知位置（无架构指引，标注为回退策略）
  [x] 将 messages 渲染至左栏；右栏摘要渲染最新版本或空态
[x] 占位与错误处理统一（AC: 4,5）
  [x] 使用 Skeleton/Empty/Error 组件，遵循 skeletonMinDurationMs、emptyMinViewMs [Source: apps/frontend/src/constants/ui.ts](apps/frontend/src/constants/ui.ts:1)
  [x] 错误态显示 trace_id、可复制；onRetry 显式触发 load()；console.info 输出诊断上下文（page/path/trace_id/retryable）[Source: docs/stories/1.8.reader-unified-placeholders-and-tests.md](docs/stories/1.8.reader-unified-placeholders-and-tests.md:19-27,32-35)
[x] 状态优先级与一致性（AC: 5）
  [x] 确保 Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady 的渲染优先级 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-29)
[x] 单测与时序推进（AC: 6）
  [x] Reader.test.tsx：覆盖恢复路径、骨架最小显隐、错误态 trace_id 显示/复制、retry→load() 成功 [Source: docs/stories/1.8.reader-unified-placeholders-and-tests.md#Testing](docs/stories/1.8.reader-unified-placeholders-and-tests.md:106-115)
  [x] 使用 fake timers 与 advanceAndFlush，规避竞态；必要时使用 mockResolvedValueOnce 模拟第二次成功 [Source: docs/stories/1.8.reader-unified-placeholders-and-tests.md](docs/stories/1.8.reader-unified-placeholders-and-tests.md:94-98)
[ ] 质量增强建议（非阻断，纳入后续故事或任务）
  [ ] 新增 409 冲突交互单测（三决策路径 + 平滑滚动）
  [ ] 新增 URL query trace_id 兜底的正路径用例
  [ ] 后端消息/摘要 ORM 落地并替换占位查询（SQL 层排序/筛选）

## Project Structure Notes
- 与架构 Monorepo 结构一致，无结构冲突：[Source: architecture.md#6.2](docs/architecture.md:168-193)
- reading_position 字段与 ERD 的 reading_state/active_chunk_index 存在命名差异：建议在 BE 层统一映射策略并在接口层给出清晰字段（本故事不落实现，作为后端任务注记）[Source: docs/architecture.md#4.1 ERD](docs/architecture.md:100-115)

## Testing
- 位置：apps/frontend/src/pages/Reader.test.tsx
- 覆盖点：
  - 首次加载显示骨架≥300ms 后才消失
  - 恢复 messages/summary 渲染；无 summary 时显示空态
  - 恢复阅读位置（模拟滚动或段落定位）成功
  - 错误态展示 trace_id 与复制；console.info 被调用
  - 点击“重试”后触发 load() 并成功进入内容态
  - trace_id 归一化优先：body.error.trace_id ＞ err.traceId ＞ URL query ＞ 随机 uuid

## Change Log
| Date       | Version | Description                           | Author |
|------------|---------|---------------------------------------|--------|
| 2025-08-06 | 0.1     | 初稿：1.9 会话断点续学与状态持久化 | Bob    |

## Dev Agent Record
（开发阶段由 Dev Agent 填写）

- Agent Model Used: openrouter/horizon-beta
- Debug Log References:
  - 前端 Reader 首载与进度恢复日志：[apps/frontend/src/pages/Reader.tsx:94-108](apps/frontend/src/pages/Reader.tsx:94-108) 中 `console.log("reader_view_open", ...)`
  - 保存进度失败与冲突分支诊断：[apps/frontend/src/pages/Reader.tsx:151-155](apps/frontend/src/pages/Reader.tsx:151-155)、[apps/frontend/src/pages/Reader.tsx:472-477](apps/frontend/src/pages/Reader.tsx:472-477)、[apps/frontend/src/pages/Reader.tsx:507-510](apps/frontend/src/pages/Reader.tsx:507-510)、[apps/frontend/src/pages/Reader.tsx:540-542](apps/frontend/src/pages/Reader.tsx:540-542)
  - 错误态信息输出（带 page/path/trace_id）：[`function ErrorState()`](apps/frontend/src/components/Placeholders.tsx:107) 内 `console.info("[ErrorState]", ...)`
- Completion Notes List:
  - 完成 Reader 中摘要区块渲染：当 `summary_latest` 存在时展示文本，否则按统一规范展示空态占位。
  - 保持原有状态优先级、骨架最小时长与 Error/Empty 交互一致。
  - 测试新增覆盖摘要存在/不存在两条路径，沿用 fake timers 与 advanceAndFlush 辅助函数，断言 aria-label="summary-latest"。
  - 现有“reading_position 优先 + 获取 ETag”用例保留并增强（提供 summary_latest 时也应通过）。
- File List:
  - 修改 Reader，新增“摘要”区块展示与空态：
    - [apps/frontend/src/pages/Reader.tsx:391-566](apps/frontend/src/pages/Reader.tsx:391)
  - 新增/更新测试：
    - 在 `Reader.test.tsx` 中新增“summary 存在时渲染文本、否则显示空态”用例，并在 existing 用例中包含 summary 数据：
      - [apps/frontend/src/pages/Reader.test.tsx:320-350](apps/frontend/src/pages/Reader.test.tsx:320-350) 扩展 “reading_position 优先”用例以含 `summary_latest`
      - [apps/frontend/src/pages/Reader.test.tsx:350-末尾](apps/frontend/src/pages/Reader.test.tsx:350) 新增 “无 summary_latest 显示空态”

## QA Results（追加复审结论）
结论：Approved（维持通过；补充一致性核查与测试证据，未发现阻断问题）

一致性与证据补充
- 契约与数据恢复：前端 `SessionDetailResponse` 字段与后端一致，包含 messages/summary_latest/reading_position；前端在首载一次性恢复 UI（对照 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:65-70)、[`apps/backend/app/models/schemas.py`](apps/backend/app/models/schemas.py:78)、[`apps/backend/app/api/routes.py`](apps/backend/app/api/routes.py:141)、[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:223-349,403-426)）。
- 统一占位与状态优先级：遵循 1.7/1.8 规范，状态优先级 Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady；错误态展示 trace_id 并支持复制，重试显式调用 load()；见 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:107-189)、[`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1-36)。
- 测试覆盖：Reader.test.tsx 覆盖恢复路径、骨架≥300ms、错误态 trace_id 显示/复制与 retry→load() 成功路径；client.test.ts 覆盖错误模型与退避。参见 [`apps/frontend/src/pages/Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:192-321,320-350) 与 [`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:23-109,238-270,369-385)。

与相关故事/改动的关系
- 与 1.6：阅读进度的拉取/提交与断点续学互补；冲突处理路径独立于错误占位，不相互覆盖。
- 与 1.7/1.8：统一占位/trace_id/重试/a11y 基线保持一致；Reader 已接入并通过测试。
- 与 1.10：真实数据状态收敛的分类与退避策略保持兼容，不需修改后端契约。

建议（非阻断，纳入后续）
- 单测补充：新增“阅读位置与 ETag 同步后的再次进入”回归用例，覆盖边界值（靠近段落边界 ±1 的定位容差）。
- 事件追踪：将“恢复来源（本地/远端）、滚动定位、摘要空态”纳入轻量埋点，便于实用户行为分析与问题定位。

最终结论
- 通过（Approved）。各 AC 达标且测试充分，无需进一步阻塞。