# 2.3.2 设备来源与默认选择提示（P1）

Status: Planned

主题：跨设备继续阅读

目标：在发生阅读进度冲突时，向用户提供“来源提示”（lastSource.deviceId/userAgent/updatedAt），并据此计算合理的默认决策项（使用远端/保留本地/合并最大），在不泄露个人隐私的前提下提升选择效率与可理解性。

参考来源
- 跨设备同步：lastSource 与并发冲突交互 [`docs/stories/1.6.cross-device-session-sync-and-resume.md`](docs/stories/1.6.cross-device-session-sync-and-resume.md:90-109,135-170)
- 统一占位与错误模型（冲突提示与错误占位独立）[`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)
- Reader 占位与测试基线（可观测性与 a11y）[`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:10-35)

Acceptance Criteria
1) 来源提示可视化（不含隐私）
- 冲突提示中展示“最近更新的设备来源”和“更新时间”，格式如：
  - 最近更新：设备 A（2 分钟前）
  - 本地设备：设备 B（刚刚）
- 设备标识以匿名 deviceId 的友好别名显示（如“设备 A/B/C”），不展示完整 UA/IP 等敏感信息；可选 hover 仅显示简化 UA（如 “Safari iOS”）。

2) 默认决策计算
- 默认选择规则：
  - 若远端更新时间更近（且差异达到 shouldPromptConflict 的阈值），默认“使用远端”；
  - 若本地更新时间更近，默认“保留本地”；
  - 当两者时间极近（≤1 分钟）且 offset 差距较大（≥阈值的 2 倍），默认“合并（取最大）”。
- 默认项以按钮高亮或预选状态提示，用户可一键确认。

3) 可配置与一致性
- 阈值依赖 2.3.1 shouldPromptConflict 的默认 minute/delta，并额外引入 tie-break 配置：
  - tieCloseMinute=1，tieLargeDeltaFactor=2（用于决定“合并最大”的默认条件）
- 常量位置：constants/ui.ts；测试不依赖具体文案。

4) a11y 与可诊断
- 提示区可读性良好（aria-live=polite），默认项具备 aria-selected。
- 控制台打印一次性 info：{ page: 'reader', defaultChoice, localUpdatedAt, remoteUpdatedAt, trace_id }。

Dev Notes
- 设备别名：首次发现 deviceId 时分配 A/B/C 标签并持久化到 localStorage 映射，避免泄露真实 deviceId。
- 时间呈现：使用相对时间（x 分钟前）+ title 提供绝对时间 tooltip。
- 与 2.3.3 的三决策交互解耦，当前文档仅定义默认项计算与展示提示。

Tasks / Subtasks
- [ ] constants：tieCloseMinute, tieLargeDeltaFactor, deviceAlias 映射策略
- [ ] UI：冲突提示文案与默认项高亮（不包含三决策完整流程）
- [ ] 诊断：一次性 info 打点、trace_id 透出
- [ ] 测试：时间近/远、差距大小、合并最大默认项场景；设备别名稳定

Testing
- Vitest + RTL
  - local 新于 remote → 默认“保留本地”
  - remote 新于 local → 默认“使用远端”
  - 极近时间 + 大差距 → 默认“合并最大”
  - 设备别名映射稳定（相同 deviceId → 相同别名）

Change Log
- 2025-08-07 v0.1 初稿（P1 定义与 AC 拟定）