# 2.1.2 Frontend：对话状态机与自动重连（P0）

Status: Planned

主题：对话流同步（前端）

目标：在 Reader/Session 对话流中落地统一状态机（idle/ready/streaming/failed/aborted），实现 SSE 断流检测与自动重连（≤10s），贯穿 AbortSignal 与统一错误模型/trace_id 的可诊断体验，确保与后端流式/最终模式一致。

参考来源
- PRD/NFR 与统一错误模型 [`docs/prd.md`](docs/prd.md:85-95,235-248)
- 架构：接口契约、横切逻辑（重试/超时/追踪）[`docs/architecture.md`](docs/architecture.md:79-96,69-74,205-216,282-301)
- Epic1：对话流/超时/占位统一/动态摘要 MVP [`docs/stories/1.11.reader-timeout-and-abortable-requests.md`](docs/stories/1.11.reader-timeout-and-abortable-requests.md:1), [`docs/stories/1.12.frontend-conversation-stream-and-dynamic-summary-mvp.md`](docs/stories/1.12.frontend-conversation-stream-and-dynamic-summary-mvp.md:1), [`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:1)
- 现有代码：SSE/客户端/Reader 容器 [`apps/frontend/src/api/sse.ts`](apps/frontend/src/api/sse.ts:1), [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1), [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)

Acceptance Criteria
1) 状态机与可视反馈
- 定义对话流状态机：idle → ready → streaming → (failed | aborted)；成功结束后回到 ready。
- UI 在 streaming 时禁用重复提交，显示进度/流式片段；failed 显示 ErrorState（含 trace_id、重试按钮）；aborted 显示轻量提示并允许重新发送。

2) SSE 断流与自动重连
- 断流识别：心跳/超时机制或 onerror/onclose 事件；断流后在≤10s 内自动重连 1 次（可配置），用户可手动重试。
- 重连恢复：基于最近一条未完成消息的上下文恢复；若后端已完成，前端合并最终片段，避免重复展示。

3) Abort 与总时限
- 发送对话时创建可中止控制器，提供“取消”操作；取消后进入 aborted 状态；资源与事件监听正确清理。
- 请求总时限遵循客户端基线（默认 10s）；命中总时限进入 failed，展示错误模型与 trace_id。

4) 错误模型与诊断
- 错误按 network/timeout/server/unknown 等归类，ErrorState 展示统一标题与“问题追踪ID：{trace_id}”，支持复制；console.info 输出 { page: 'conversation', trace_id, path, retryable }。
- 来自 body.error.trace_id 或响应头 X-Trace-Id 的 traceId 优先级：body.error.trace_id ＞ err.traceId ＞ URL/query ＞ uuid。

5) 与动态摘要/消息顺序一致
- 流式片段与最终消息顺序一致；重复/乱序去重；摘要“已更新”提示与对应消息定位一致，不抖动。
- SSE → 最终 JSON 回落路径渲染一致，UI 无差异。

Dev Notes
- 发送 API：sendMessage(sessionId, text, options) 支持 streaming=true/false；内部复用 retryWithBackoff + linkAbortSignals。
- SSE 解析：解析 event: token/usage/end/error；end 携带 messageId/trace_id；error 统一归口 ApiError。
- 资源清理：组件卸载或路由切换中止挂起流与定时器，避免泄漏。
- 可配置常量：reconnectDelayMs=2000、reconnectMaxAttempts=1、totalTimeoutMs=10000。

Tasks / Subtasks
- [ ] 状态机：ready/streaming/failed/aborted 实装与 UI 绑定
- [ ] SSE 自动重连：断流识别与 1 次重连，超时与成功路径
- [ ] Abort/总时限：前端中止与 10s 总时限协同；竞态清理
- [ ] 错误模型：ErrorState + trace_id 复制 + console.info 诊断
- [ ] 摘要联动：流式→最终，摘要提示与消息定位一致
- [ ] 测试：fake timers 推进流、重连、错误→重试恢复

Testing
- Reader/Session 容器（Vitest + RTL）
  - 正常流：streaming 片段渲染，end 收敛，状态机回 ready
  - 断流→自动重连：≤10s 内恢复一次，成功收敛；失败显示 ErrorState
  - 取消：点击取消进入 aborted，不再接收片段；再次发送可重新进入 streaming
  - 超时：10s 命中进入 failed；trace_id 展示与复制；重试→成功
  - 摘要提示：收到 end 后显示“摘要已更新”徽标，点击定位消息

Change Log
- 2025-08-07 v0.1 初稿（P0 定义与 AC 拟定）