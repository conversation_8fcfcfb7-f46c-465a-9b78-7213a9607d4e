<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>知深学习导师 · 学习会话</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>tailwind.config = { theme: { extend: { colors: { brand: { DEFAULT: '#6E56CF' } } } } }</script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Source+Serif+Pro:wght@400;600&family=Geist+Mono:wght@400;500&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="zhishen_theme_1.css" />
  <script src="https://unpkg.com/flowbite@2.0.0/dist/flowbite.min.js"></script>
  <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
  <style>
    .sticky-header{ position: sticky; top: 0; z-index: 50; background: var(--background); backdrop-filter: saturate(1.2) blur(6px); border-bottom: 1px solid var(--border); }
    .chat-bubble-ai{ background: var(--secondary); border:1px solid var(--border); border-radius: var(--radius-md); box-shadow: var(--shadow-xs); }
    .chat-bubble-user{ background: white; border:1px solid var(--border); border-radius: var(--radius-md); box-shadow: var(--shadow-xs); }
    .toolbar-btn{ border:1px solid var(--border); border-radius: 10px; background: white; }
    .tab-btn{ padding: 8px 12px; border-radius: 999px; border:1px solid var(--border); }
    .tab-btn.active{ background: var(--foreground); color: white; border-color: var(--foreground); }
    .status-dot{ width:10px; height:10px; border-radius:999px; display:inline-block; }
    .status-saving{ background: oklch(0.75 0.19 70); }
    .status-saved{ background: oklch(0.72 0.05 150); }
    .status-error{ background: oklch(0.60 0.23 27); }
    .reader-pane{ height: calc(100dvh - 64px); }
    .chat-pane{ height: calc(100dvh - 64px); }
    .input-area{ border-top:1px solid var(--border); background: var(--card); }
  </style>
</head>
<body class="min-h-screen">
  <!-- Header -->
  <header class="sticky-header">
    <div class="mx-auto max-w-[1400px] px-4 h-16 flex items-center justify-between">
      <div class="flex items-center gap-2">
        <button class="toolbar-btn px-2 py-1 flex items-center gap-1"><i data-lucide="arrow-left"></i><span>Sessions</span></button>
        <div class="h-6 w-px bg-[var(--border)] mx-2"></div>
        <div class="text-sm text-[oklch(0.35_0_0)]">知深学习导师</div>
        <div class="font-medium ml-2">会话标题</div>
      </div>
      <div class="flex items-center gap-3">
        <div class="flex items-center gap-2 text-sm">
          <span class="status-dot status-saved"></span>
          <span class="text-[oklch(0.40_0_0)]">已保存</span>
        </div>
        <button class="toolbar-btn px-2 py-1" title="清空对话"><i data-lucide="eraser"></i></button>
        <button class="toolbar-btn px-2 py-1" title="导出"><i data-lucide="download"></i></button>
        <button class="toolbar-btn px-2 py-1" title="帮助"><i data-lucide="help-circle"></i></button>
      </div>
    </div>
  </header>

  <!-- Main -->
  <main class="mx-auto max-w-[1400px] px-4 grid grid-cols-1 lg:grid-cols-12 gap-4">
    <!-- Chat Column -->
    <section class="lg:col-span-7 chat-pane flex flex-col">
      <!-- Onboarding banner -->
      <div class="card p-3 text-sm mb-2">
        粘贴文本已创建会话。继续与你的“导师”对话，右侧将实时生成/更新摘要。
      </div>

      <!-- Chat stream -->
      <div id="chatStream" class="flex-1 overflow-y-auto pr-1 space-y-3">
        <!-- AI message sample -->
        <div class="chat-bubble-ai p-3">
          <div class="text-xs text-[oklch(0.45_0_0)] mb-1">AI · 刚刚</div>
          <div class="prose max-w-none">
            基于你提供的材料，我们可以先从“三层结构摘要 + 关键术语清单”开始，帮助你建立全局框架。
          </div>
          <div class="mt-2 flex gap-2 flex-wrap">
            <span class="badge px-2 py-0.5 text-xs cursor-pointer" onclick="jumpToOriginal()">引用：第2段</span>
            <span class="badge px-2 py-0.5 text-xs">建议：先问目标</span>
          </div>
        </div>
        <!-- User message sample -->
        <div class="flex justify-end">
          <div class="chat-bubble-user p-3 max-w-[80%]">
            <div class="text-xs text-[oklch(0.45_0_0)] mb-1 text-right">你 · 刚刚</div>
            <div>请先给出三层结构摘要，并列出关键术语。</div>
          </div>
        </div>
        <!-- AI streaming placeholder -->
        <div class="chat-bubble-ai p-3" id="aiTyping">
          <div class="text-xs text-[oklch(0.45_0_0)] mb-1">AI · 生成中…</div>
          <div class="flex items-center gap-1 text-[oklch(0.40_0_0)]"><span class="animate-bounce">●</span><span class="animate-bounce [animation-delay:150ms]">●</span><span class="animate-bounce [animation-delay:300ms]">●</span></div>
        </div>
      </div>

      <!-- Template suggestions -->
      <details class="mt-3">
        <summary class="cursor-pointer text-sm text-[oklch(0.40_0_0)]">＋ 模板建议</summary>
        <div class="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-2">
          <button class="button-primary px-3 py-2 text-sm">结构化提问</button>
          <button class="button-primary px-3 py-2 text-sm">反思检查表</button>
          <button class="button-primary px-3 py-2 text-sm">关键术语测验</button>
        </div>
      </details>

      <!-- Input area -->
      <div class="input-area mt-3 p-2 rounded-t-[var(--radius-md)]">
        <div class="flex items-end gap-2">
          <button class="toolbar-btn px-2 py-1" title="粘贴/附件"><i data-lucide="paperclip"></i></button>
          <textarea rows="1" placeholder="输入你的问题，或粘贴文本…" class="flex-1 resize-none p-3 input" oninput="autoResize(this)"></textarea>
          <button class="button-primary px-3 py-2 flex items-center gap-1"><i data-lucide="send"></i><span>发送</span></button>
        </div>
        <div class="mt-1 text-right text-xs text-[oklch(0.45_0_0)]">自动保存已开启</div>
      </div>
    </section>

    <!-- Reader Column -->
    <aside class="lg:col-span-5 reader-pane flex flex-col">
      <!-- Tabs -->
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <button class="tab-btn active" id="tabOriginal" onclick="switchPane('original')">原文</button>
          <button class="tab-btn" id="tabSummary" onclick="switchPane('summary')">摘要</button>
        </div>
        <div class="text-xs text-[oklch(0.45_0_0)]" id="summaryStatus">已更新</div>
      </div>

      <!-- Original pane -->
      <div id="paneOriginal" class="card p-4 overflow-y-auto flex-1">
        <h2 class="text-lg font-semibold mb-2">材料标题：某某报告</h2>
        <article class="prose max-w-none">
          <p>第一段：这是示例原文。知深学习导师强调围绕材料进行深度对话与动态摘要。</p>
          <p id="p2" class="reader-highlight">第二段：动态上下文管理以当前材料为核心，避免对话漂移，并支持可追溯性。</p>
          <p>第三段：自动保存与断点续学让你随时中断后快速恢复。</p>
          <p>第四段：后续将支持多格式导入与知识网络化。</p>
        </article>
      </div>

      <!-- Summary pane -->
      <div id="paneSummary" class="card p-4 overflow-y-auto flex-1 hidden">
        <div class="space-y-3">
          <div class="summary-node">
            <div class="flex items-center justify-between">
              <div class="font-medium">一、核心目标</div>
              <button class="text-sm text-[oklch(0.40_0_0)]" onclick="jumpToOriginal()">跳转原文</button>
            </div>
            <p class="mt-1 text-[oklch(0.35_0_0)]">围绕特定材料进行有记忆的深度对话学习，避免浅层问答。</p>
          </div>
          <div class="summary-node">
            <div class="flex items-center justify-between">
              <div class="font-medium">二、关键机制</div>
              <button class="text-sm text-[oklch(0.40_0_0)]" onclick="jumpToOriginal()">跳转原文</button>
            </div>
            <ul class="list-disc ml-5 text-[oklch(0.35_0_0)]">
              <li>动态上下文管理</li>
              <li>可交互摘要</li>
              <li>自动保存/断点续学</li>
            </ul>
          </div>
          <div class="summary-node">
            <div class="flex items-center justify-between">
              <div class="font-medium">三、MVP范围</div>
              <button class="text-sm text-[oklch(0.40_0_0)]" onclick="jumpToOriginal()">跳转原文</button>
            </div>
            <p class="mt-1 text-[oklch(0.35_0_0)]">粘贴文本导入、对话式学习与动态摘要、会话自动保存与恢复。</p>
          </div>
        </div>
      </div>
    </aside>
  </main>

  <script>
    function autoResize(t){ t.style.height='auto'; t.style.height = (t.scrollHeight)+'px'; }
    function switchPane(key){
      const isOriginal = key==='original';
      document.getElementById('paneOriginal').classList.toggle('hidden', !isOriginal);
      document.getElementById('paneSummary').classList.toggle('hidden', isOriginal);
      document.getElementById('tabOriginal').classList.toggle('active', isOriginal);
      document.getElementById('tabSummary').classList.toggle('active', !isOriginal);
    }
    function jumpToOriginal(){
      switchPane('original');
      const el = document.getElementById('p2');
      if(!el) return;
      el.classList.add('flash');
      el.scrollIntoView({behavior:'smooth', block:'center'});
      setTimeout(()=> el.classList.remove('flash'), 1800);
    }
    window.addEventListener('DOMContentLoaded', ()=> { lucide.createIcons(); });
  </script>
</body>
</html>