:root {
  --background: oklch(0.985 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.40 0.03 300);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.96 0 0);
  --muted-foreground: oklch(0.56 0 0);
  --accent: oklch(0.92 0.03 295);
  --accent-foreground: oklch(0.2 0 0);
  --destructive: oklch(0.60 0.23 27);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.92 0 0);
  --input: oklch(0.92 0 0);
  --ring: oklch(0.72 0 0);
  --chart-1: oklch(0.70 0.10 270);
  --chart-2: oklch(0.60 0.12 300);
  --chart-3: oklch(0.58 0.14 320);
  --chart-4: oklch(0.52 0.12 345);
  --chart-5: oklch(0.48 0.10 20);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.92 0 0);
  --sidebar-ring: oklch(0.72 0 0);
  --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-serif: 'Source Serif Pro', ui-serif, Georgia, Cambria, 'Times New Roman', Times, serif;
  --font-mono: 'Geist Mono', 'JetBrains Mono', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  --radius: 12px;
  --spacing: 0.25rem;
  --shadow-2xs: 0 1px 2px 0 hsl(0 0% 0% / 0.04);
  --shadow-xs: 0 1px 3px 0 hsl(0 0% 0% / 0.06);
  --shadow-sm: 0 2px 4px -1px hsl(0 0% 0% / 0.08), 0 1px 3px 0 hsl(0 0% 0% / 0.06);
  --shadow: 0 4px 6px -1px hsl(0 0% 0% / 0.08), 0 2px 4px -2px hsl(0 0% 0% / 0.06);
  --shadow-md: 0 8px 12px -4px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 12px 20px -6px hsl(0 0% 0% / 0.12);
  --shadow-xl: 0 20px 32px -10px hsl(0 0% 0% / 0.16);

  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
}

/* Base resets aligned with Tailwind/Flowbite */
html, body { height: 100% !important; }
body { background: var(--background) !important; color: var(--foreground) !important; font-family: var(--font-sans) !important; -webkit-font-smoothing: antialiased !important; }
h1, h2, h3, h4, h5, h6 { color: var(--foreground) !important; font-weight: 650 !important; letter-spacing: -0.01em !important; }
p { color: oklch(0.30 0 0) !important; }
a { color: color-mix(in oklch, var(--primary) 60%, black) !important; text-decoration: none !important; }
a:hover { text-decoration: underline !important; }

.card { background: var(--card) !important; color: var(--card-foreground) !important; border: 1px solid var(--border) !important; border-radius: var(--radius-md) !important; box-shadow: var(--shadow-xs) !important; }
.button-primary { background: var(--primary) !important; color: var(--primary-foreground) !important; border-radius: 10px !important; }
.button-primary:hover { filter: brightness(1.04) !important; }
.input { background: white !important; border: 1px solid var(--input) !important; color: var(--foreground) !important; border-radius: 10px !important; }
.badge { background: var(--accent) !important; color: var(--accent-foreground) !important; border-radius: 999px !important; }

/* Reader specific helpers */
.reader-highlight { background: color-mix(in oklch, var(--accent) 40%, white) !important; transition: background 400ms ease !important; }
.reader-highlight.flash { background: color-mix(in oklch, var(--accent) 70%, white) !important; }
.summary-node { border-left: 3px solid var(--accent) !important; padding-left: 12px !important; margin-left: 6px !important; }

/* Tabs */
.tab-active { color: var(--foreground) !important; }
.tab-inactive { color: var(--muted-foreground) !important; }
