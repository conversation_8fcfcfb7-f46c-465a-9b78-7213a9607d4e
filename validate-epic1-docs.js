#!/usr/bin/env node

/**
 * Epic 1 文档更新验证脚本
 * 验证所有相关文档是否正确更新了 Epic 1 的实施结果
 */

const fs = require('fs');
const path = require('path');

let testCount = 0;
let passedCount = 0;
let failedCount = 0;

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  testCount++;
  if (condition) {
    passedCount++;
    log(`PASS: ${message}`, 'success');
  } else {
    failedCount++;
    log(`FAIL: ${message}`, 'error');
  }
}

function readFile(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    log(`无法读取文件 ${filePath}: ${error.message}`, 'error');
    return null;
  }
}

// 验证 Story 1.17 文档更新
function validateStory117() {
  log('验证 Story 1.17 文档更新');
  
  const filePath = 'docs/stories/1.17.epic1-tech-debt-remediation-p0-sse-errors-llm-gray-rollout.md';
  const content = readFile(filePath);
  
  if (!content) {
    assert(false, 'Story 1.17 文档存在');
    return;
  }
  
  assert(content.includes('End-to-End Testing Results'), 'Story 1.17 包含端到端测试结果');
  assert(content.includes('**后端测试**: ✅ 34/34 全部通过'), 'Story 1.17 包含后端测试结果');
  assert(content.includes('**前端测试**: ⚠️ 30/31 通过'), 'Story 1.17 包含前端测试结果');
  assert(content.includes('**用户体验测试**: ✅ 33/33 全部通过'), 'Story 1.17 包含用户体验测试结果');
  assert(content.includes('Production Ready'), 'Story 1.17 标记为生产就绪');
  assert(content.includes('端到端测试完成'), 'Story 1.17 Change Log 包含测试完成记录');
  
  // 验证所有验收条件都标记为完成
  const acPatterns = [
    'AC1 前端 SSE 稳健性落地 ✅',
    'AC2 统一错误分类与 trace 贯穿 ✅',
    'AC3 LLM 适配层集成测试与灰度回退 ✅',
    'AC4 策略一致性与配置集中 ✅',
    'AC5 测试覆盖与通过门槛 ✅'
  ];
  
  acPatterns.forEach((pattern, index) => {
    assert(content.includes(pattern), `Story 1.17 验收条件 AC${index + 1} 标记完成`);
  });
}

// 验证测试策略文档更新
function validateTestingStrategy() {
  log('验证测试策略文档更新');
  
  const filePath = 'docs/testing-strategy-guide.md';
  const content = readFile(filePath);
  
  if (!content) {
    assert(false, '测试策略文档存在');
    return;
  }
  
  assert(content.includes('Epic 1 测试实施结果与经验总结'), '测试策略文档包含 Epic 1 结果');
  assert(content.includes('**单元测试**: ✅ 34/34 (100% 通过率)'), '测试策略文档包含后端测试统计');
  assert(content.includes('**单元测试**: ⚠️ 30/31 (96.8% 通过率)'), '测试策略文档包含前端测试统计');
  assert(content.includes('**用户体验测试**: ✅ 33/33 (100% 通过率)'), '测试策略文档包含用户体验测试统计');
  assert(content.includes('测试门槛标准更新'), '测试策略文档包含门槛标准更新');
  assert(content.includes('Epic 1 实施记录'), '测试策略文档包含 Epic 1 参考链接');
  assert(content.includes('最后更新：2025-08-07'), '测试策略文档包含更新日期');
}

// 验证架构文档
function validateArchitecture() {
  log('验证架构文档');
  
  const filePath = 'docs/architecture.md';
  const content = readFile(filePath);
  
  if (!content) {
    assert(false, '架构文档存在');
    return;
  }
  
  // 验证关键架构概念是否存在
  assert(content.includes('trace_id'), '架构文档包含 trace_id 概念');
  assert(content.includes('SSE'), '架构文档包含 SSE 概念');
  assert(content.includes('错误'), '架构文档包含错误处理概念');
  assert(content.includes('前端客户端（apiClient）：统一请求、错误模型、trace_id 注入'), '架构文档包含前端客户端描述');
  assert(content.includes('前端微架构：客户端 SSE 解析与中止链路'), '架构文档包含 SSE 微架构描述');
}

// 验证 OpenAPI 文档
function validateOpenAPI() {
  log('验证 OpenAPI 文档');
  
  const filePath = 'docs/openapi.yaml';
  const content = readFile(filePath);
  
  if (!content) {
    assert(false, 'OpenAPI 文档存在');
    return;
  }
  
  assert(content.includes('X-Trace-Id'), 'OpenAPI 文档包含 X-Trace-Id 头定义');
  assert(content.includes('ErrorResponse'), 'OpenAPI 文档包含错误响应模型');
  assert(content.includes('trace_id'), 'OpenAPI 文档包含 trace_id 字段');
  assert(content.includes('统一错误模型与 trace_id'), 'OpenAPI 文档描述包含 trace_id 说明');
  
  // 验证所有主要端点都包含 trace_id
  const traceIdCount = (content.match(/X-Trace-Id:/g) || []).length;
  assert(traceIdCount >= 5, `OpenAPI 文档至少5个端点包含 X-Trace-Id (实际: ${traceIdCount})`);
}

// 验证文档间的一致性
function validateConsistency() {
  log('验证文档间一致性');
  
  const story117 = readFile('docs/stories/1.17.epic1-tech-debt-remediation-p0-sse-errors-llm-gray-rollout.md');
  const testingStrategy = readFile('docs/testing-strategy-guide.md');
  
  if (!story117 || !testingStrategy) {
    assert(false, '所需文档都存在');
    return;
  }
  
  // 验证测试结果数字一致性
  assert(
    story117.includes('34/34') && testingStrategy.includes('34/34'),
    '后端测试结果在不同文档中一致'
  );
  
  assert(
    story117.includes('30/31') && testingStrategy.includes('30/31'),
    '前端测试结果在不同文档中一致'
  );
  
  assert(
    story117.includes('33/33') && testingStrategy.includes('33/33'),
    '用户体验测试结果在不同文档中一致'
  );
}

// 验证文档完整性
function validateCompleteness() {
  log('验证文档完整性');
  
  const requiredFiles = [
    'docs/stories/1.17.epic1-tech-debt-remediation-p0-sse-errors-llm-gray-rollout.md',
    'docs/testing-strategy-guide.md',
    'docs/architecture.md',
    'docs/openapi.yaml'
  ];
  
  requiredFiles.forEach(filePath => {
    const exists = fs.existsSync(filePath);
    assert(exists, `必需文档存在: ${filePath}`);
  });
  
  // 验证测试脚本文件存在
  const testScripts = [
    'test-epic1-e2e.js',
    'test-sse-streaming.js',
    'test-user-experience.js'
  ];
  
  testScripts.forEach(scriptPath => {
    const exists = fs.existsSync(scriptPath);
    assert(exists, `测试脚本存在: ${scriptPath}`);
  });
}

// 主验证函数
async function runValidation() {
  log('🚀 开始 Epic 1 文档更新验证');
  log('='.repeat(60));
  
  validateCompleteness();
  validateStory117();
  validateTestingStrategy();
  validateArchitecture();
  validateOpenAPI();
  validateConsistency();
  
  log('='.repeat(60));
  log(`📊 文档验证结果: ${passedCount}/${testCount} 通过`);
  
  if (failedCount === 0) {
    log('🎉 所有文档验证通过！Epic 1 文档更新完成', 'success');
  } else {
    log(`⚠️ 有 ${failedCount} 个验证失败，需要检查`, 'warning');
  }
  
  log('\n📋 Epic 1 文档更新总结:');
  log('   ✅ Story 1.17: 添加端到端测试结果和生产就绪状态');
  log('   ✅ 测试策略指南: 添加测试覆盖率和经验总结');
  log('   ✅ 架构文档: SSE、trace_id、错误处理架构已完整');
  log('   ✅ OpenAPI 文档: trace_id 和错误模型定义完整');
  log('   ✅ 测试脚本: 端到端、SSE、用户体验测试脚本已生成');
}

// 运行验证
runValidation().catch(error => {
  log(`文档验证失败: ${error.message}`, 'error');
  process.exit(1);
});
