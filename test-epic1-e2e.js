#!/usr/bin/env node

/**
 * Epic 1 端到端测试脚本
 * 测试 SSE 稳健性、错误处理、trace_id 贯穿等功能
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:5173';

// 测试用例计数器
let testCount = 0;
let passedCount = 0;
let failedCount = 0;

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  testCount++;
  if (condition) {
    passedCount++;
    log(`PASS: ${message}`, 'success');
  } else {
    failedCount++;
    log(`FAIL: ${message}`, 'error');
  }
}

// HTTP 请求辅助函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 测试 1: 后端健康检查
async function testHealthCheck() {
  log('测试 1: 后端健康检查');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: '/health',
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 200, '健康检查返回 200');
    assert(response.body.status === 'healthy', '健康状态正确');
    assert(response.body.service === 'zhiread-backend', '服务名称正确');
  } catch (error) {
    log(`健康检查失败: ${error.message}`, 'error');
  }
}

// 测试 2: 创建会话并验证 trace_id
async function testCreateSession() {
  log('测试 2: 创建会话并验证 trace_id');
  try {
    const testText = '这是一个测试文本，用于验证 Epic 1 的功能实现。包含中文字符和标点符号。';
    
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: '/api/sessions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    }, { text: testText });

    assert(response.status === 201, '会话创建返回 201');
    assert(response.body.id, '返回会话 ID');
    assert(response.body.text === testText, '文本内容正确');
    assert(response.headers['x-trace-id'], 'trace_id 在响应头中');
    
    // 保存会话 ID 供后续测试使用
    global.testSessionId = response.body.id;
    global.testTraceId = response.headers['x-trace-id'];
    
    log(`创建的会话 ID: ${global.testSessionId}`);
    log(`Trace ID: ${global.testTraceId}`);
    
  } catch (error) {
    log(`创建会话失败: ${error.message}`, 'error');
  }
}

// 测试 3: 获取会话列表
async function testGetSessions() {
  log('测试 3: 获取会话列表');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: '/api/sessions?limit=10&offset=0',
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 200, '获取会话列表返回 200');
    assert(Array.isArray(response.body.sessions), '返回会话数组');
    assert(response.body.total >= 1, '至少有一个会话');
    assert(response.headers['x-trace-id'], 'trace_id 在响应头中');
    
    // 验证刚创建的会话在列表中
    const createdSession = response.body.sessions.find(s => s.id === global.testSessionId);
    assert(createdSession, '创建的会话在列表中');
    
  } catch (error) {
    log(`获取会话列表失败: ${error.message}`, 'error');
  }
}

// 测试 4: 获取单个会话详情
async function testGetSessionById() {
  log('测试 4: 获取单个会话详情');
  try {
    if (!global.testSessionId) {
      log('跳过测试：没有可用的会话 ID', 'error');
      return;
    }

    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: `/api/sessions/${global.testSessionId}`,
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 200, '获取会话详情返回 200');
    assert(response.body.id === global.testSessionId, '会话 ID 匹配');
    assert(response.body.text, '包含文本内容');
    assert(response.headers['x-trace-id'], 'trace_id 在响应头中');
    
  } catch (error) {
    log(`获取会话详情失败: ${error.message}`, 'error');
  }
}

// 测试 5: 错误处理 - 获取不存在的会话
async function testGetNonexistentSession() {
  log('测试 5: 错误处理 - 获取不存在的会话');
  try {
    const fakeId = '00000000-0000-0000-0000-000000000000';
    
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: `/api/sessions/${fakeId}`,
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 404, '不存在的会话返回 404');
    assert(response.headers['x-trace-id'], '错误响应也包含 trace_id');
    
  } catch (error) {
    log(`错误处理测试失败: ${error.message}`, 'error');
  }
}

// 测试 6: 前端可访问性
async function testFrontendAccessibility() {
  log('测试 6: 前端可访问性');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 5173,
      path: '/',
      method: 'GET',
      headers: { 'Accept': 'text/html' }
    });

    assert(response.status === 200, '前端页面可访问');
    assert(typeof response.body === 'string', '返回 HTML 内容');
    
  } catch (error) {
    log(`前端可访问性测试失败: ${error.message}`, 'error');
  }
}

// 主测试函数
async function runTests() {
  log('🚀 开始 Epic 1 端到端测试');
  log('='.repeat(50));
  
  await testHealthCheck();
  await testCreateSession();
  await testGetSessions();
  await testGetSessionById();
  await testGetNonexistentSession();
  await testFrontendAccessibility();
  
  log('='.repeat(50));
  log(`📊 测试结果: ${passedCount}/${testCount} 通过`);
  
  if (failedCount === 0) {
    log('🎉 所有测试通过！Epic 1 基础功能正常', 'success');
  } else {
    log(`⚠️  有 ${failedCount} 个测试失败，需要检查`, 'error');
  }
  
  // 输出服务状态
  log('\n📋 服务状态:');
  log(`   后端: ${BASE_URL}`);
  log(`   前端: ${FRONTEND_URL}`);
  log('\n💡 你现在可以在浏览器中访问前端进行手动测试');
}

// 运行测试
runTests().catch(error => {
  log(`测试运行失败: ${error.message}`, 'error');
  process.exit(1);
});
