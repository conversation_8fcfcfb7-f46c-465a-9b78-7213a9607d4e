#!/usr/bin/env node

/**
 * 用户体验综合测试脚本
 * 模拟真实用户操作流程，测试 Epic 1 的端到端功能
 */

const http = require('http');

const BASE_URL = 'http://localhost:8000';
const FRONTEND_URL = 'http://localhost:5173';

let testCount = 0;
let passedCount = 0;
let failedCount = 0;

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function assert(condition, message) {
  testCount++;
  if (condition) {
    passedCount++;
    log(`PASS: ${message}`, 'success');
  } else {
    failedCount++;
    log(`FAIL: ${message}`, 'error');
  }
}

// HTTP 请求辅助函数
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const jsonBody = body ? JSON.parse(body) : {};
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// 用户场景 1: 创建学习会话
async function userScenario1_CreateSession() {
  log('🎯 用户场景 1: 创建学习会话');
  
  const testTexts = [
    '人工智能（AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。',
    'React 是一个用于构建用户界面的 JavaScript 库。它由 Facebook 开发，现在由 Meta 维护。React 的核心概念包括组件、状态管理、生命周期方法和虚拟 DOM。',
    'Epic 1 技术债整改测试：这是一个用于验证 SSE 稳健性、统一错误处理和 LLM 灰度验证的综合测试文本。包含中文和英文内容，用于测试系统的多语言处理能力。'
  ];
  
  const createdSessions = [];
  
  for (let i = 0; i < testTexts.length; i++) {
    try {
      log(`📝 创建会话 ${i + 1}: ${testTexts[i].substring(0, 30)}...`);
      
      const response = await makeRequest({
        hostname: 'localhost',
        port: 8000,
        path: '/api/sessions',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }, { text: testTexts[i] });

      assert(response.status === 201, `会话 ${i + 1} 创建成功 (201)`);
      assert(response.body.id, `会话 ${i + 1} 返回有效 ID`);
      assert(response.headers['x-trace-id'], `会话 ${i + 1} 包含 trace_id`);
      
      if (response.body.id) {
        createdSessions.push({
          id: response.body.id,
          title: response.body.title,
          traceId: response.headers['x-trace-id']
        });
        log(`   ✅ 会话 ${i + 1} ID: ${response.body.id}`);
      }
      
      // 模拟用户操作间隔
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      log(`❌ 创建会话 ${i + 1} 失败: ${error.message}`, 'error');
    }
  }
  
  global.userSessions = createdSessions;
  log(`📊 用户场景 1 完成: 创建了 ${createdSessions.length} 个会话`);
}

// 用户场景 2: 浏览会话列表
async function userScenario2_BrowseSessions() {
  log('🎯 用户场景 2: 浏览会话列表');
  
  try {
    // 获取会话列表
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: '/api/sessions?limit=20&offset=0',
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 200, '会话列表获取成功');
    assert(response.body.items && Array.isArray(response.body.items), '返回会话数组');
    assert(response.body.total >= 0, '返回总数信息');
    assert(response.headers['x-trace-id'], '列表请求包含 trace_id');
    
    const sessions = response.body.items;
    log(`📋 找到 ${sessions.length} 个会话 (总计: ${response.body.total})`);
    
    // 验证会话数据结构
    if (sessions.length > 0) {
      const firstSession = sessions[0];
      assert(firstSession.id, '会话包含 ID');
      assert(firstSession.title, '会话包含标题');
      assert(firstSession.created_at, '会话包含创建时间');
      
      log(`   📄 最新会话: ${firstSession.title.substring(0, 50)}...`);
    }
    
  } catch (error) {
    log(`❌ 浏览会话列表失败: ${error.message}`, 'error');
  }
}

// 用户场景 3: 查看会话详情
async function userScenario3_ViewSessionDetails() {
  log('🎯 用户场景 3: 查看会话详情');
  
  if (!global.userSessions || global.userSessions.length === 0) {
    log('⚠️ 没有可用的会话进行详情查看', 'warning');
    return;
  }
  
  const session = global.userSessions[0];
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8000,
      path: `/api/sessions/${session.id}`,
      method: 'GET',
      headers: { 'Accept': 'application/json' }
    });

    assert(response.status === 200, '会话详情获取成功');
    assert(response.body.id === session.id, '会话 ID 匹配');
    assert(response.body.title, '包含会话标题');
    assert(response.body.content, '包含会话内容');
    assert(response.headers['x-trace-id'], '详情请求包含 trace_id');
    
    log(`📖 会话详情: ${response.body.title}`);
    log(`   📝 内容段落数: ${response.body.content?.paragraphs?.length || 0}`);
    log(`   💬 消息数: ${response.body.messages?.length || 0}`);
    log(`   📊 阅读进度: ${response.body.reading_position || 0}%`);
    
  } catch (error) {
    log(`❌ 查看会话详情失败: ${error.message}`, 'error');
  }
}

// 用户场景 4: 发送消息对话
async function userScenario4_SendMessage() {
  log('🎯 用户场景 4: 发送消息对话');
  
  if (!global.userSessions || global.userSessions.length === 0) {
    log('⚠️ 没有可用的会话进行消息发送', 'warning');
    return;
  }
  
  const session = global.userSessions[0];
  const testMessages = [
    '请帮我总结一下这篇文章的主要内容',
    '这个主题有哪些关键概念？',
    '能否举个具体的例子来说明？'
  ];
  
  for (let i = 0; i < testMessages.length; i++) {
    try {
      log(`💬 发送消息 ${i + 1}: ${testMessages[i]}`);
      
      const response = await makeRequest({
        hostname: 'localhost',
        port: 8000,
        path: `/api/sessions/${session.id}/messages`,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }, { content: testMessages[i] });

      assert(response.status === 201, `消息 ${i + 1} 发送成功`);
      assert(response.headers['x-trace-id'], `消息 ${i + 1} 包含 trace_id`);
      
      if (response.body.content) {
        log(`   🤖 AI 回复: ${response.body.content.substring(0, 100)}...`);
      }
      
      // 模拟用户思考时间
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      log(`❌ 发送消息 ${i + 1} 失败: ${error.message}`, 'error');
    }
  }
}

// 用户场景 5: 错误处理测试
async function userScenario5_ErrorHandling() {
  log('🎯 用户场景 5: 错误处理测试');
  
  const errorTests = [
    {
      name: '访问不存在的会话',
      path: '/api/sessions/00000000-0000-0000-0000-000000000000',
      method: 'GET',
      expectedStatus: 404
    },
    {
      name: '无效的会话 ID 格式',
      path: '/api/sessions/invalid-uuid',
      method: 'GET',
      expectedStatus: 422
    },
    {
      name: '空的会话创建请求',
      path: '/api/sessions',
      method: 'POST',
      data: {},
      expectedStatus: 422
    }
  ];
  
  for (const test of errorTests) {
    try {
      log(`🔍 测试: ${test.name}`);
      
      const response = await makeRequest({
        hostname: 'localhost',
        port: 8000,
        path: test.path,
        method: test.method,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }, test.data);

      assert(
        response.status === test.expectedStatus || response.status >= 400,
        `${test.name} 返回预期的错误状态码`
      );
      assert(response.headers['x-trace-id'], `${test.name} 错误响应包含 trace_id`);
      
      log(`   📊 状态码: ${response.status}, Trace ID: ${response.headers['x-trace-id']}`);
      
    } catch (error) {
      log(`❌ 错误测试失败: ${test.name} - ${error.message}`, 'error');
    }
  }
}

// 主测试函数
async function runUserExperienceTests() {
  log('🚀 开始用户体验综合测试');
  log('='.repeat(70));
  
  await userScenario1_CreateSession();
  await userScenario2_BrowseSessions();
  await userScenario3_ViewSessionDetails();
  await userScenario4_SendMessage();
  await userScenario5_ErrorHandling();
  
  log('='.repeat(70));
  log(`📊 用户体验测试结果: ${passedCount}/${testCount} 通过`);
  
  if (failedCount === 0) {
    log('🎉 所有用户体验测试通过！系统运行正常', 'success');
  } else {
    log(`⚠️ 有 ${failedCount} 个测试失败，需要检查`, 'warning');
  }
  
  log('\n🌟 Epic 1 用户体验测试完成！');
  log(`   📱 前端地址: ${FRONTEND_URL}`);
  log(`   🔧 后端地址: ${BASE_URL}`);
  log('\n💡 你现在可以在浏览器中访问前端进行手动验证');
}

// 运行测试
runUserExperienceTests().catch(error => {
  log(`用户体验测试运行失败: ${error.message}`, 'error');
  process.exit(1);
});
