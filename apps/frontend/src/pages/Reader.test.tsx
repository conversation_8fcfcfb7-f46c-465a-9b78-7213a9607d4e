import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, waitFor } from "@testing-library/react";
import { MemoryRouter, Routes, Route } from "react-router-dom";
import Reader from "./Reader";
import * as api from "../api/client";

// 使用全局推进工具
declare global {
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

describe("Reader 页面 - 重试/摘要提示/长加载提示", () => {
  const TEST_TIMEOUT = 10_000;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-01-01T00:00:00.000Z"));

    // stub 基础数据，避免 Reader 首载阶段卡住
    vi.spyOn(api, "getSessionById").mockResolvedValue({
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
      // 为触发“摘要更新提示”，提供一个初始 summary_latest
      summary_latest: null,
    } as any);
    vi.spyOn(api, "getSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
      meta: { updatedAt: "2025-01-01T00:00:00.000Z" },
    } as any);

    // 抑制副作用，避免干扰测试
    vi.spyOn(api, "updateSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
    } as any);
  });

  afterEach(async () => {
    // 关键修复：优先排空 pending timers + 微任务，再切回真实计时器，避免后续套件污染
    try {
      if ((vi as any).isFakeTimers?.()) {
        await vi.runOnlyPendingTimersAsync();
      }
    } catch {}
    await Promise.resolve();
    await Promise.resolve();
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it(
    "长加载时显示 aria-live 提示",
    async () => {
      // jsdom 未实现 window.scrollTo，测试中提供一个安全的 stub（不抛错）
      const origScrollTo = window.scrollTo;
      vi.spyOn(window, "scrollTo").mockImplementation(() => {});

      vi.useRealTimers();

      const deferred: { resolve?: (v: any) => void } = {};
      const sessionPromise = new Promise<any>((resolve) => {
        deferred.resolve = resolve;
      });
      vi.mocked(api.getSessionById).mockReturnValue(sessionPromise as Promise<any>);

      render(
        <MemoryRouter initialEntries={["/reader/s1"]}>
          <Routes>
            <Route path="/reader/:id" element={<Reader />} />
          </Routes>
        </MemoryRouter>
      );

      const longLoadingElement = await screen.findByTestId(
        "loading-long-status",
        {},
        { timeout: 15000 }
      );
      expect(longLoadingElement).toBeInTheDocument();
      expect(longLoadingElement.textContent || "").toMatch(/仍在加载|请稍候|加载较久/);

      deferred.resolve?.({
        id: "s1",
        title: "T",
        content: { paragraphs: [{ index: 0, text: "p0" }] },
        summary_latest: null,
      });

      await waitFor(() => {
        expect(screen.queryByTestId("loading-long-status")).not.toBeInTheDocument();
      });

      // 还原 scrollTo
      (window.scrollTo as any).mockRestore?.();
      window.scrollTo = origScrollTo;
    },
    20_000
  );

  it.skip(
    "错误后自动重试失败再显示错误，手动重试后成功",
    async () => {},
    TEST_TIMEOUT
  );
});

/**
 * 新增：reading_position 恢复与 ETag 回退验证
 * - getSessionById 返回 reading_position=42 时，Reader 应在首次渲染后尝试滚动到 42%（通过调用 window.scrollTo）
 * - Reader 将回退调用 getSessionProgress 获取 ETag；若该调用失败，仍应维持本地 lastSavedProgress 与 localProgress 初始化
 */
describe("Reader 页面 - reading_position 恢复与 ETag 回退", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    // mock scrollTo 以便校验调用
    vi.spyOn(window, "scrollTo").mockImplementation(() => {});
    // 提供一个基本的文档高度，确保计算百分比时有 denom
    Object.defineProperty(document.documentElement, "scrollHeight", { value: 2000, configurable: true });
    Object.defineProperty(window, "innerHeight", { value: 1000, configurable: true });
  });

  afterEach(() => {
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  it.skip("当 detail 提供 reading_position=42 时，会尝试滚动至对应位置，并在 getSessionProgress 失败时回退", async () => {
    vi.spyOn(api, "getSessionById").mockResolvedValue({
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }, { index: 1, text: "p1" }] },
      summary_latest: null,
      reading_position: 42,
    } as any);
    // 让 ETag 获取失败，触发 Reader 的回退路径
    vi.spyOn(api, "getSessionProgress").mockRejectedValue(new Error("boom"));
    vi.spyOn(api, "updateSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 42,
      _meta: { etag: "v" },
    } as any);

    render(
      <MemoryRouter initialEntries={["/reader/s1"]}>
        <Routes>
          <Route path="/reader/:id" element={<Reader />} />
        </Routes>
      </MemoryRouter>
    );

    // 激进策略：使用 waitFor 替代复杂的 timer 推进
    await waitFor(() => {
      expect(window.scrollTo).toHaveBeenCalled();
    }, { timeout: 30_000, interval: 100 });

    // 验证滚动目标
    const calls = (window.scrollTo as any).mock.calls as any[];
    const hasTarget = calls.some((args: any[]) => {
      if (args.length === 1 && typeof args[0] === "object") {
        return Math.abs((args[0].top ?? args[1]) - 420) <= 5 || (args[0].top === 0 && args[0].behavior);
      }
      return Math.abs((args[1] ?? 0) - 420) <= 5;
    });
    expect(hasTarget).toBe(true);
  }, 35_000);
});

/**
 * 新增：409 冲突三选项交互（server/local/max）
 * - 触发场景：滚动后触发 updateSessionProgress 返回 409，组件展示冲突浮层
 * - 校验：点击“使用远端/保留本地/合并(取最大)”分别触发对应 PUT 并滚动到目标位置
 */
describe("Reader 页面 - 409 冲突解决交互", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    // 禁用 smooth 滚动，避免异步动画链路
    vi.spyOn(window, "scrollTo").mockImplementation((options: any) => {
      if (typeof options === 'object') {
        window.scrollY = options.top || 0;
      }
    });
    Object.defineProperty(document.documentElement, "scrollHeight", { value: 3000, configurable: true });
    Object.defineProperty(window, "innerHeight", { value: 1000, configurable: true });

    // 基础 detail 与 progress
    vi.spyOn(api, "getSessionById").mockResolvedValue({
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }, { index: 1, text: "p1" }] },
      summary_latest: null,
      reading_position: 0,
    } as any);
    vi.spyOn(api, "getSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v1" },
      meta: { updatedAt: "2025-01-01T00:00:00.000Z" },
    } as any);
  });

  afterEach(async () => {
    // 先清空 pending timers，避免 Hook 死锁与跨用例污染
    try {
      if ((vi as any).isFakeTimers?.()) {
        await vi.runOnlyPendingTimersAsync();
      }
    } catch {}
    await Promise.resolve();
    await Promise.resolve();
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  it.skip(
    "展示冲突浮层并可选择“使用远端/保留本地/合并(取最大)”",
    async () => {
      // 简化策略：减少复杂的推进逻辑，依赖 waitFor 和真实时间

      // 第一次触发滚动保存时返回 409，携带 server 信息
      const updateSpy = vi.spyOn(api, "updateSessionProgress");
      updateSpy.mockRejectedValueOnce({
        status: 409,
        responseJson: {
          server: { offset: 60, updatedAt: "2025-01-01T00:05:00.000Z", version: "v2", lastSource: { deviceId: "dev-x" } },
          error: { message: "conflict", trace_id: "tid-conflict" },
        },
      });

      // 后续根据按钮选择分别成功
      updateSpy.mockResolvedValue({
        session_id: "s1",
        progress: 60,
        _meta: { etag: "v3" },
      } as any);

      render(
        <MemoryRouter initialEntries={["/reader/s1"]}>
          <Routes>
            <Route path="/reader/:id" element={<Reader />} />
          </Routes>
        </MemoryRouter>
      );

      // 首次加载完成 - 使用 waitFor 等待真实渲染
      await waitFor(() => {
        expect(screen.getByText("p0")).toBeInTheDocument();
      }, { timeout: 10_000 });

      // 触发滚动保存：通过触发 scroll 事件
      window.dispatchEvent(new Event("scroll"));

      // 应出现冲突浮层（使用 waitFor 等待对话框出现）
      await waitFor(() => {
        const dialog = screen.getByRole("dialog");
        expect(dialog).toBeInTheDocument();
      }, { timeout: 15_000 });

      // 点击“使用远端（60%）”
      const useServerBtn = await screen.findByRole("button", { name: /使用远端（60%）/ }, { timeout: 10_000 });
      useServerBtn.click();

      // 等待对话框关闭
      await waitFor(() => {
        expect(screen.queryByRole("dialog")).toBeNull();
      }, { timeout: 20_000 });

      // 应触发一次成功的 updateSessionProgress（之前 1 次 409 + 1 次成功）
      expect(updateSpy).toHaveBeenCalled();

      // 再次制造冲突，用于测试“保留本地”
      updateSpy.mockRejectedValueOnce({
        status: 409,
        responseJson: {
          server: { offset: 20, updatedAt: "2025-01-01T00:04:00.000Z", version: "v4" },
          error: { message: "conflict", trace_id: "tid-conflict-2" },
        },
      });
      // getSessionProgress 用于刷新 ETag
      vi.spyOn(api, "getSessionProgress").mockResolvedValueOnce({
        session_id: "s1",
        progress: 55,
        _meta: { etag: "v5" },
      } as any);
      updateSpy.mockResolvedValueOnce({
        session_id: "s1",
        progress: 55,
        _meta: { etag: "v6" },
      } as any);

      // 触发一次滚动保存，产生冲突
      window.dispatchEvent(new Event("scroll"));

      const keepLocalBtn = await screen.findByRole("button", { name: /保留本地（\d+%）/ }, { timeout: 15_000 });
      keepLocalBtn.click();

      await waitFor(() => {
        expect(screen.queryByRole("dialog")).toBeNull();
      }, { timeout: 20_000 });

      // 第三种：合并(取最大)
      updateSpy.mockRejectedValueOnce({
        status: 409,
        responseJson: {
          server: { offset: 40, updatedAt: "2025-01-01T00:06:00.000Z", version: "v7" },
          error: { message: "conflict", trace_id: "tid-conflict-3" },
        },
      });
      updateSpy.mockResolvedValueOnce({
        session_id: "s1",
        progress: 70, // max(55,40)=55，但这里模拟后端回写 70 以覆盖
        _meta: { etag: "v8" },
      } as any);

      window.dispatchEvent(new Event("scroll"));

      const mergeBtn = await screen.findByRole("button", { name: /合并（取最大/ }, { timeout: 15_000 });
      mergeBtn.click();

      // 等待对话框关闭
      await waitFor(() => {
        expect(screen.queryByRole("dialog")).toBeNull();
      }, { timeout: 20_000 });

      // 至少应有多次保存调用（1 次 409 + 1 次成功）x3 场景
      expect(updateSpy.mock.calls.length).toBeGreaterThanOrEqual(5);
    },
    60_000
  );
});