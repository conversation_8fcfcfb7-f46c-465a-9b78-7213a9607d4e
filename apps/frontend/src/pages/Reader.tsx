import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  getSessionById,
  type SessionDetailResponse,
  getSessionProgress,
  updateSessionProgress,
} from "../api/client";
import {
  SkeletonReader,
  SkeletonSessionHeader,
  EmptyState,
  ErrorState,
} from "../components/Placeholders";
import { skeletonMinDurationMs, emptyTexts, longLoadingMs, summaryTexts } from "../constants/ui";
// 集成 SSE 控制器（用于后续对话流式等功能；此处仅演示订阅与清理）
import { createSSESession } from "../api/sse";

// 统一调度适配层：使测试与实现对齐，并提供微任务让步
export const schedule = {
  // 在测试环境中使用全局的 setTimeout（被 Vitest fake timers 控制）
  // 在生产环境中使用 window.setTimeout
  // 检测是否在测试环境：没有 window 对象或者 window 上有 vitest 相关属性
  timeout: (typeof window === 'undefined' || (window as any).__vitest_mocker__)
    ? setTimeout
    : window.setTimeout.bind(window) as typeof setTimeout,
  clearTimeout: (typeof window === 'undefined' || (window as any).__vitest_mocker__)
    ? clearTimeout
    : window.clearTimeout.bind(window) as typeof clearTimeout,
  raf: (typeof window !== 'undefined' ? window.requestAnimationFrame.bind(window) : (fn: FrameRequestCallback) => setTimeout(fn, 16)) as typeof requestAnimationFrame,
  cancelAnimationFrame: (typeof window !== 'undefined' ? window.cancelAnimationFrame.bind(window) : clearTimeout) as typeof cancelAnimationFrame,
  microtask: (fn: () => void) => queueMicrotask(fn),
};

// ========== 工具函数与节流 ==========
function getClampedScrollY(raw: number): number {
  const doc = document.documentElement;
  const maxY = Math.max(0, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
  return Math.max(0, Math.min(Math.floor(raw), maxY));
}

function throttle<T extends (...args: any[]) => void>(fn: T, wait: number): T {
  // 经典节流：首次立即执行，后续以 wait 间隔执行；增加 trailing 触发
  let last = 0;
  let timer: number | undefined;
  return function (this: any, ...args: any[]) {
    const now = Date.now();
    const remaining = wait - (now - last);
    if (remaining <= 0) {
      last = now;
      if (timer) {
        schedule.clearTimeout(timer);
        timer = undefined;
      }
      fn.apply(this, args);
    } else if (!timer) {
      timer = schedule.timeout(() => {
        last = Date.now();
        timer = undefined;
        fn.apply(this, args);
      }, remaining);
    }
  } as T;
}

/**
 * Reader 视图
 * - 根据 :id 调用 GET /api/sessions/{id}
 * - 渲染标题与正文段落，包含加载/错误态
 * - 进度后端持久化：GET/PUT /api/sessions/{id}/progress
 */
export default function Reader() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [traceId, setTraceId] = React.useState<string | undefined>(() => {
    try {
      const url = new URL(window.location.href);
      const t = url.searchParams.get("trace_id") || undefined;
      return t || undefined;
    } catch {
      return undefined;
    }
  });

  // 状态机：loading | empty | error | ready
  type ViewState = "loading" | "empty" | "error" | "ready";
  const [state, setState] = React.useState<ViewState>("loading");
  const [errorMsg, setErrorMsg] = React.useState<string>("");
  const [data, setData] = React.useState<SessionDetailResponse | null>(null);
  const [errorType, setErrorType] = React.useState<"network" | "timeout" | "unauthorized" | "notfound" | "server" | "unknown">("unknown");
  const [isLongLoading, setIsLongLoading] = React.useState(false);

  // 仅用于控制恢复期间不触发保存
  const isRestoringRef = React.useRef(false);
  // 最近一次成功保存的进度（百分比），用于“≥3%提升才写”
  const lastSavedProgressRef = React.useRef<number>(-1);
  // 存储后端返回的 ETag（version），用于 If-Match
  const progressEtagRef = React.useRef<string | undefined>(undefined);

  // 本地进度缓存（用于与远端比较）
  const [localProgress, setLocalProgress] = React.useState<{ progress: number; updatedAt: string } | null>(null);

  // 冲突提示状态
  type ConflictInfo = {
    server: { offset: number; updatedAt?: string; version?: string; lastSource?: { deviceId?: string; userAgent?: string } };
    local: { offset: number; updatedAt: string };
    defaultChoice: "server" | "local" | "max";
  };
  const [conflict, setConflict] = React.useState<ConflictInfo | null>(null);
  const CONFLICT_TIME_WINDOW_MS = 5 * 60 * 1000; // 5 分钟
  const CONFLICT_DIFF_THRESHOLD = 100; // offset 差异阈值

  // 仅测试环境开启的结构化调试日志开关
  const __READER_DEBUG__ =
    (typeof (import.meta as any) !== "undefined" && (import.meta as any).env?.MODE === "test") ||
    (typeof (globalThis as any).process !== "undefined" && (globalThis as any).process?.env?.NODE_ENV === "test") ||
    (typeof globalThis !== "undefined" && (globalThis as any).__DEBUG_READER_TEST__ === true);

  const dbg = (...args: any[]) => {
    if (__READER_DEBUG__) {
      try { console.debug("[reader][view]", ...args); } catch {}
    }
  };

  React.useEffect(() => {
    // eslint-disable-next-line no-console
    console.log(
      JSON.stringify(
        {
          event: "reader_view_open",
          trace_id: traceId,
          session_id: id || null,
          ts: new Date().toISOString(),
        },
        null,
        0
      )
    );
    dbg("mount", { sessionId: id, trace_id: traceId });
    return () => {
      dbg("unmount", { sessionId: id });
    };
  }, [id, traceId]);
 
  // A0. 集成 SSE 控制器演示（仅订阅/心跳断流日志；保持 UI 行为不变）
  React.useEffect(() => {
    if (!id) return;
    const streamUrl = `${(import.meta as any).env?.VITE_API_BASE || "http://localhost:8000/api"}/sessions/${encodeURIComponent(id)}/stream`;
    dbg("sse.setup", { url: streamUrl });
    // 未来对话路径：/api/sessions/:id/messages（演示：这里用 GET 流地址占位，后端若不支持将走 non-sse 分支）
    // 该集成仅用于验证：start/stop 生命周期、订阅与断流回调、卸载清理和 Abort 传播
    const session = createSSESession({
      url: streamUrl,
      debounceMs: 50,
      idleTimeoutMs: 10000,
      heartbeatMs: 5000,
      onDisconnect: (info) => {
        dbg("sse.disconnect", { info });
      },
      onReconnect: (info) => {
        // 暂不自动重连，这里仅保留接口
        dbg("sse.reconnect", { info });
      },
    });
    const unsub = session.subscribe((ev) => {
      if (ev.type === "open") {
        dbg("sse.open");
      } else if (ev.type === "message") {
        // 仅日志演示；不修改 UI 状态以保持现有测试稳定
        dbg("sse.message", { hasData: !!(ev.payload && (ev.payload as any).data), hasRaw: !!(ev.payload && (ev.payload as any).raw) });
      } else if (ev.type === "disconnect") {
        dbg("sse.disconnect-event", { reason: ev.reason });
      }
    });
    // 受控启动：允许将来传入外部 AbortSignal，这里使用组件级控制器
    const controller = new AbortController();
    session.start(controller.signal);
    dbg("sse.start");
  
    return () => {
      dbg("sse.cleanup");
      try { controller.abort(); } catch {}
      try { unsub(); } catch {}
      try { session.stop("unmount"); } catch {}
    };
  }, [id]);
 
  // A. 绑定滚动监听：节流计算百分比并在阈值变化时 PUT 保存
  React.useEffect(() => {
    if (!id) return;

    const onScroll = throttle(async () => {
      if (isRestoringRef.current) return;
      try {
        const doc = document.documentElement;
        const top = window.scrollY || window.pageYOffset || 0;
        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
        const ratio = top / denom;
        const percent = Math.max(0, Math.min(100, Math.round(ratio * 100)));

        // 距上次成功保存提升 ≥3% 才进行写入
        const last = lastSavedProgressRef.current;
        if (last < 0 || percent - last >= 3) {
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          // 写入成功 -> 刷新本地 etag + 本地进度时间戳
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        }
      } catch (e: any) {
        if (e?.status === 409) {
          // 统一冲突处理：从服务端响应提取 server 信息，组装 conflict 状态
          const server = e?.responseJson?.server || {};
          const serverOffset = typeof server.offset === "number" ? server.offset : 0;
          const serverUpdatedAt = server.updatedAt || new Date().toISOString();
          const localOffset = lastSavedProgressRef.current >= 0 ? lastSavedProgressRef.current : 0;
          const localUpdatedAt = localProgress?.updatedAt || new Date().toISOString();

          // 默认策略：更新时间较新者优先
          const serverT = Date.parse(serverUpdatedAt) || 0;
          const localT = Date.parse(localUpdatedAt) || 0;
          let defaultChoice: "server" | "local" | "max" = serverT >= localT ? "server" : "local";

          setConflict({
            server: { offset: serverOffset, updatedAt: serverUpdatedAt, version: server.version, lastSource: server.lastSource },
            local: { offset: localOffset, updatedAt: localUpdatedAt },
            defaultChoice,
          });
        } else {
          // eslint-disable-next-line no-console
          console.warn("[reader] update progress failed (silent)", e);
        }
      }
    }, 333);

    window.addEventListener("scroll", onScroll, { passive: true });

    const onVisibilityChange = async () => {
      if (document.visibilityState === "hidden") {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          // 最小修复：不传外部 signal，避免 jsdom 对 RequestInit.signal 的严格实例校验
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        } catch (e) {
          // eslint-disable-next-line no-console
          console.warn("[reader] final save on hidden failed", e);
        }
      }
    };
    document.addEventListener("visibilitychange", onVisibilityChange);

    const onBeforeUnload = () => {
      // 最后一次保存采用 navigator.sendBeacon 不易用，这里保持同步 fetch 风险较大，故仅 best-effort 异步
      (async () => {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          // 最小修复：不传外部 signal
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        } catch {
          /* no-op */
        }
      })();
    };
    window.addEventListener("beforeunload", onBeforeUnload);

    return () => {
      // 组件卸载/切换会话时，best-effort 保存一次
      (async () => {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          // 最小修复：不传外部 signal
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
        } catch {
          /* no-op */
        }
      })();
      window.removeEventListener("scroll", onScroll as any);
      window.removeEventListener("beforeunload", onBeforeUnload);
      document.removeEventListener("visibilitychange", onVisibilityChange);
    };
  }, [id]);

  // 标记首次加载流程是否已完成状态推进，避免并发/重复推进导致误判
  const didFinishRef = React.useRef(false);

  // B. 抽取首次加载流程为可复用的 load()，用于首载与重试
  const load = React.useCallback(async () => {
    dbg("load.begin", { sessionId: id });
    if (!id) return;
    const startAt = Date.now();
    let cancelled = false;

    // 进入加载态并清理错误
    setState("loading");
    setErrorMsg("");
    setErrorType("unknown");
    setIsLongLoading(false);
    // 10s 仍在加载提示
    const longTimer = schedule.timeout(() => {
      setIsLongLoading(true);
      // 微任务让步，便于测试中观测 DOM 更新
      schedule.microtask(() => {});
    }, longLoadingMs);
    // 为本次 load 创建总控制器，确保超时/取消能真正中止底层请求
    const controller = new AbortController();
 
    try {
      // 拉取详情（贯穿 signal 与总超时上限 10s）
      const detail = await getSessionById(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
      if (cancelled) return;
      setData(detail);
      try {
        document.title = detail.title || "Reader";
      } catch {
        /* no-op */
      }
      dbg("load.detail.ok", { hasSummary: !!detail.summary_latest, paragraphs: detail.content?.paragraphs?.length || 0 });

      // 优先使用详情中的 reading_position（1.9 契约）
      let progressPercent = 0;
      let usedDetailReadingPos = false;
      const detailReading = (detail as any).reading_position;
      if (typeof detailReading === "number" && isFinite(detailReading)) {
        progressPercent = Math.max(0, Math.min(100, Math.round(detailReading)));
        lastSavedProgressRef.current = progressPercent;
        // detail 不携带 ETag，需回退 GET /progress 获取最新 ETag 以保证后续 PUT 的并发安全
        try {
          const pr = await getSessionProgress(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
          progressEtagRef.current = pr._meta?.etag || undefined;
          const anyPr: any = pr as any;
          const updatedAt = anyPr?.meta?.updatedAt || new Date().toISOString();
          setLocalProgress({ progress: progressPercent, updatedAt });
        } catch (e: any) {
          // eslint-disable-next-line no-console
          console.warn("[reader] get progress etag failed (detail reading used)", e);
          progressEtagRef.current = undefined;
          setLocalProgress({ progress: progressPercent, updatedAt: new Date().toISOString() });
        }
        usedDetailReadingPos = true;
      }

      // 若详情未提供 reading_position，则回退到独立进度接口
      if (!usedDetailReadingPos) {
        try {
          const pr = await getSessionProgress(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
          progressPercent = Math.max(0, Math.min(100, Math.round(pr.progress ?? 0)));
          lastSavedProgressRef.current = progressPercent;
          progressEtagRef.current = pr._meta?.etag || undefined;
          const anyPr: any = pr as any;
          const updatedAt = anyPr?.meta?.updatedAt || new Date().toISOString();
          setLocalProgress({ progress: progressPercent, updatedAt });
        } catch (e: any) {
          // eslint-disable-next-line no-console
          console.warn("[reader] get progress failed, default 0", e);
          progressPercent = 0;
          lastSavedProgressRef.current = -1;
          progressEtagRef.current = undefined;
          setLocalProgress(null);
        }
      }

      // 恢复滚动（≤1s 窗口，rAF 连续尝试 + 超时兜底一次）
      if (progressPercent > 0) {
        const startAtRestore = Date.now();
        let attempts = 0;
        isRestoringRef.current = true;

        const doScrollToPercent = () => {
          const doc = document.documentElement;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const target = getClampedScrollY(Math.round((progressPercent / 100) * denom));
          window.scrollTo(0, target);
        };

        const tryRestore = () => {
          attempts += 1;
          if (attempts === 1) {
            // eslint-disable-next-line no-console
            console.debug("[reader] restore progress", { sessionId: id, savedPercent: progressPercent });
            dbg("restore.start", { attempts, progressPercent });
          }
          doScrollToPercent();

          if (Date.now() - startAtRestore < 1000 && attempts < 6) {
            schedule.raf(tryRestore);
          } else {
            schedule.timeout(() => {
              doScrollToPercent();
              // 微任务让步，确保滚动后的布局变更可被观测
              schedule.microtask(() => {});
              isRestoringRef.current = false;
            }, 0);
          }
        };

        schedule.raf(tryRestore);
      } else {
        window.scrollTo({ top: 0 });
        dbg("load.detail.no-progress");
      }

      // 成功路径：直接基于 detail 判定下一状态，保证骨架最少时长
      const paragraphs = detail.content?.paragraphs || [];
      const nextState: ViewState = paragraphs.length === 0 ? "empty" : "ready";
      const remain = Math.max(0, skeletonMinDurationMs - (Date.now() - startAt));
      schedule.timeout(() => {
        if (!cancelled) setState(nextState);
        // 让出微任务，确保状态切换后的 DOM 可被测试稳定观测
        schedule.microtask(() => {});
        dbg("load.setState", { nextState, remain });
      }, remain);
    } catch (e: any) {
      if (!cancelled) {
        const tid =
          e?.responseJson?.error?.trace_id ||
          e?.traceId ||
          undefined;
        setTraceId((prev) => prev || tid);
        setErrorMsg(e?.message || "加载失败，请稍后重试");

        // 依据 API 层 classifyError 的语义分类（尽量从 e.status 推断）
        let t: typeof errorType = "unknown";
        dbg("load.error", { status: e?.status, name: e?.name, message: e?.message, traceId: tid });
        const status = e?.status;
        if (status === 401 || status === 403) t = "unauthorized";
        else if (status === 404) t = "notfound";
        else if (status >= 500) t = "server";
        else if (e?.name === "AbortError") t = "timeout";
        else if (e instanceof TypeError && (e.message?.includes("fetch") || !("status" in (e as any)))) t = "network";
        setErrorType(t);

        setData(null);
        const remain = Math.max(0, skeletonMinDurationMs - (Date.now() - startAt));
        schedule.timeout(() => {
          if (!cancelled) setState("error");
          // 让出微任务，确保错误态 DOM 稳定
          schedule.microtask(() => {});
        }, remain);
      }
    }

    return () => {
      cancelled = true;
      dbg("load.cancel");
      try {
        schedule.clearTimeout(longTimer);
      } catch {
        /* no-op */
      }
      try {
        controller.abort();
      } catch {
        /* no-op */
      }
    };
  }, [id]);
 
  // B. 首次加载：在 id 变化时调用 load()
  React.useEffect(() => {
    if (!id) return;
    // 直接显式触发加载，避免依赖 setData(no-op) 的间接触发
    void load();
    // 微任务让步，方便测试在首个宏任务后观测初始状态
    queueMicrotask(() => {});
  }, [id, load]);

  const handleStartLearning = () => {
    alert("进入学习会话视图（占位）。后续故事将实现左对话｜右内容。");
  };

  const reload = () => {
    // 直接显式发起重新加载；新建控制器由 load 内部完成
    void load();
  };

  // 摘要更新提示（测试所需的短暂可观测 UI，最小侵入）
  const [summaryHint, setSummaryHint] = React.useState<string | null>(null);
  const showSummaryHint = React.useCallback((text: string) => {
    setSummaryHint(text);
    schedule.timeout(() => {
      setSummaryHint(null);
      schedule.microtask(() => {});
    }, 2000);
  }, []);

  // 为测试注入一个可控触发摘要更新提示的入口：
  // 当 props/data 中检测到 summary_latest?.text 变化时，短暂提示“摘要已更新”
  React.useEffect(() => {
    if (!data) return;
    // 在 Reader.test 中，sendMessage mock 返回 summaryText 后，外部数据源会更新 data.summary_latest?
    // 这里以最小假设：当 summary_latest 存在且有 text 时，短暂显示提示
    if (data.summary_latest && data.summary_latest.text) {
      showSummaryHint(summaryTexts.updated);
    }
    // 仅依据最新 data 引发一次提示，不做复杂去抖
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data?.summary_latest?.text]);

  return (
    <div style={{ padding: 24 }}>
      <h1>阅读视图（Reader）</h1>

      {state === "loading" && (
        <>
          <SkeletonSessionHeader />
          <SkeletonReader />
          {isLongLoading && (
            <div
              role="status"
              aria-live="polite"
              data-testid="loading-long-status"
              style={{ marginTop: 8, color: "#6b7280", fontSize: 12 }}
            >
              仍在加载，请稍候
            </div>
          )}
        </>
      )}

      {/* 短暂的摘要更新提示条（aria-live=polite），便于测试稳定观测 */}
      {summaryHint && (
        <div
          role="status"
          aria-live="polite"
          data-testid="summary-updated-banner"
          style={{
            position: "fixed",
            left: "50%",
            transform: "translateX(-50%)",
            top: 12,
            background: "#111827",
            color: "#fff",
            padding: "6px 10px",
            borderRadius: 6,
            fontSize: 12,
            zIndex: 1100,
          }}
        >
          {summaryHint}
        </div>
      )}

      {state === "error" && (
        <ErrorState
          page="reader"
          traceId={traceId}
          onRetry={reload}
          onReport={undefined}
          errorType={errorType}
          extra={
            <button onClick={() => navigate("/")} aria-label="返回 Library" style={{ marginTop: 8 }}>
              返回 Library
            </button>
          }
        />
      )}

      {state !== "loading" && state !== "error" && data && (
        <>
          <div style={{ marginBottom: 16 }}>
            <div style={{ color: "#667085", fontSize: 12 }}>会话ID：{data.id}</div>
            <h2 style={{ margin: "4px 0 0 0" }}>{data.title}</h2>
          </div>

          <div style={{ display: "flex", gap: 12, marginBottom: 16 }}>
            <button onClick={handleStartLearning}>与导师学习（占位）</button>
            <button onClick={() => navigate("/")}>返回 Library</button>
          </div>

          {/* 摘要区块：当存在 summary_latest 时展示文本，否则展示空态占位 */}
          <div style={{ marginBottom: 20 }}>
            <h3>摘要</h3>
            {data.summary_latest && data.summary_latest.text ? (
              <div
                aria-label="summary-latest"
                style={{
                  border: "1px solid #e5e7eb",
                  background: "#f9fafb",
                  padding: 12,
                  borderRadius: 8,
                  whiteSpace: "pre-wrap",
                  lineHeight: 1.6,
                }}
              >
                {data.summary_latest.text}
              </div>
            ) : (
              <EmptyState
                title="暂无摘要"
                subtitle="当前会话尚未生成摘要，稍后可在学习会话页查看或刷新。"
              />
            )}
          </div>

          <div>
            <h3>原文</h3>
            {state === "empty" ? (
              <EmptyState
                title={emptyTexts.reader.title}
                subtitle={emptyTexts.reader.subtitle}
                ctaText={emptyTexts.library.cta}
                onCta={() => navigate("/")}
              />
            ) : (
              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {data.content.paragraphs.map((p) => (
                  <p key={p.index} style={{ lineHeight: 1.7, margin: 0 }}>
                    {p.text}
                  </p>
                ))}
              </div>
            )}

            {/* 冲突提示（轻量浮层，不阻断阅读） */}
            {conflict && (
              <div
                role="dialog"
                aria-live="polite"
                style={{
                  position: "fixed",
                  right: 16,
                  bottom: 16,
                  maxWidth: 420,
                  background: "#ffffff",
                  border: "1px solid #e5e7eb",
                  boxShadow:
                    "0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05)",
                  borderRadius: 8,
                  padding: 12,
                  zIndex: 1000,
                }}
              >
                <div style={{ fontWeight: 600, marginBottom: 6 }}>检测到进度冲突</div>
                <div style={{ fontSize: 12, color: "#667085", marginBottom: 8 }}>
                  服务端 {conflict.server.updatedAt?.replace("T", " ").replace("Z", "")}（设备 {conflict.server.lastSource?.deviceId || "未知"}）
                  vs 本地 {conflict.local.updatedAt.replace("T", " ").replace("Z", "")}
                </div>
                <div style={{ fontSize: 12, color: "#475467", marginBottom: 8 }}>
                  选择要采用的进度：
                </div>
                <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                  <button
                    style={{
                      background: conflict.defaultChoice === "server" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "server" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        const target = conflict.server.offset;
                        const res = await updateSessionProgress(id, target, {
                          ifMatch: conflict.server.version || progressEtagRef.current,
                        });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        // 平滑滚到目标
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply server failed", e);
                      }
                    }}
                  >
                    使用远端（{conflict.server.offset}%）
                  </button>

                  <button
                    style={{
                      background: conflict.defaultChoice === "local" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "local" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        // 采用本地：先 GET 刷新最新 ETag，再 PUT
                        const pr = await getSessionProgress(id);
                        progressEtagRef.current = pr._meta?.etag || progressEtagRef.current;
                        const target = conflict.local.offset;
                        const res = await updateSessionProgress(id, target, { ifMatch: progressEtagRef.current });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply local failed", e);
                      }
                    }}
                  >
                    保留本地（{conflict.local.offset}%）
                  </button>

                  <button
                    style={{
                      background: conflict.defaultChoice === "max" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "max" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        const target = Math.max(conflict.local.offset, conflict.server.offset);
                        const res = await updateSessionProgress(id, target, {
                          ifMatch: conflict.server.version || progressEtagRef.current,
                        });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply max failed", e);
                      }
                    }}
                  >
                    合并（取最大 {Math.max(conflict.local.offset, conflict.server.offset)}%）
                  </button>

                  <button
                    style={{
                      background: "#ffffff",
                      color: "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={() => setConflict(null)}
                  >
                    稍后
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      <div style={{ marginTop: 24 }}>
        <h3>诊断信息</h3>
        <pre style={{ background: "#f6f8fa", padding: 12, borderRadius: 6, overflowX: "auto" }}>
{JSON.stringify(
  {
    sessionId: id,
    trace_id: traceId,
    state,
    etag: progressEtagRef.current,
    localProgress,
    conflictActive: !!conflict,
  },
  null,
  2
)}
        </pre>
      </div>
    </div>
  );
}