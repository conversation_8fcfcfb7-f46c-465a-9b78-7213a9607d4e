/**
 * SSE 工具与类型（Phase 2）
 * - linkAbortSignals：统一中止联动与清理
 * - parseSSEStream：UTF-8 解码、按事件分割、data 聚合、JSON 安全解析
 * - createSSESession：单一会话控制器（心跳/断流检测、事件去抖、订阅管理、受控启动/停止）
 */
const __SSE_DEBUG__ =
  // 浏览器与测试通用：优先使用 import.meta.env.MODE
  (typeof (globalThis as any).importMeta !== "undefined" && (globalThis as any).importMeta?.env?.MODE === "test") ||
  (typeof (import.meta as any) !== "undefined" && (import.meta as any).env?.MODE === "test") ||
  // Node 测试环境（避免直接引用 process 类型）
  (typeof (globalThis as any).process !== "undefined" && (globalThis as any).process?.env?.NODE_ENV === "test") ||
  // 手动开关
  (typeof globalThis !== "undefined" && (globalThis as any).__DEBUG_SSE_TEST__ === true);
 
export type SSEEvent<T = unknown> = {
  event?: string;
  data?: T;
  raw?: string; // 坏 JSON 或未识别文本
};
 
export type SSEControllerEvent<T = any> =
  | { type: "open" }
  | { type: "message"; payload: SSEEvent<T> }
  | { type: "disconnect"; reason?: string }
  | { type: "reconnect"; attempt?: number };
 
export type Unsubscribe = () => void;
 
export interface SSESessionOptions {
  // 连接地址与请求参数
  url: string;
  init?: RequestInit;
  // 心跳与断流检测（仅检测，不主动重连）
  heartbeatMs?: number;     // 服务端心跳事件期望间隔（如 5s）；用于“收到任何数据”更新心跳
  idleTimeoutMs?: number;   // 若超过此时长未收到任何数据，则视为断流，触发 onDisconnect
  // 事件去抖：聚合 burst，降低渲染频率
  debounceMs?: number;      // 默认 50ms
  // 回调：仅信号，具体处理通过 subscribe 事件流
  onDisconnect?: (info: { lastEventAt?: number; reason?: string }) => void;
  onReconnect?: (info: { attempt: number }) => void; // 预留接口（本故事不主动重连，仅暴露）
}
 
/**
 * 将多个外部 AbortSignal 链接到内部 controller.signal，任一中止即中止 controller。
 * 返回 cleanup() 以移除监听。
 */
export function linkAbortSignals(controller: AbortController, ...signals: (AbortSignal | undefined)[]): () => void {
  const onAbortMap = new Map<AbortSignal, (this: AbortSignal, ev: Event) => any>();
 
  const abortWithReason = (s: AbortSignal) => {
    try {
      (controller as any).abort(s.reason);
    } catch {
      controller.abort();
    }
  };
 
  for (const s of signals) {
    if (!s) continue;
    if (s.aborted) {
      abortWithReason(s);
      continue;
    }
    const onAbort = () => abortWithReason(s);
    onAbortMap.set(s, onAbort);
    s.addEventListener("abort", onAbort, { once: true });
  }
 
  return () => {
    for (const [s, handler] of onAbortMap.entries()) {
      try {
        s.removeEventListener("abort", handler as any);
      } catch {
        // no-op
      }
    }
    onAbortMap.clear();
  };
}
 
/**
 * 解析 text/event-stream
 * - 逐块解码为文本，按 \n\n 作为事件边界
 * - 支持 event: 与 data: 行，data: 可多行拼接
 * - data 为 JSON 时尝试解析，失败则以 { raw } 上报不中断
 * - signal 中止将尝试取消 reader
 */
export async function parseSSEStream<T = unknown>(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  onEvent: (evt: SSEEvent<T>) => void,
  signal?: AbortSignal
): Promise<void> {
  const decoder = new TextDecoder("utf-8");
  let buf = "";
  let __seq = 0;
  const dbg = (...args: any[]) => {
    if (__SSE_DEBUG__) {
      try { console.debug("[sse][parse]", ...args); } catch {}
    }
  };
  dbg("start", { hasSignal: !!signal });
 
  // 若提供中止信号，则在中止时尝试取消 reader
  let aborted = false;
  const onAbort = async () => {
    aborted = true;
    dbg("abort-signal");
    try {
      await reader.cancel();
    } catch {
      // ignore
    }
  };
  if (signal) {
    if (signal.aborted) {
      await onAbort();
      throw new DOMException("The operation was aborted.", "AbortError");
    }
    signal.addEventListener("abort", onAbort, { once: true });
  }
 
  try {
    let pendingEventType: string | undefined;
    let pendingDataLines: string[] = [];
 
    const flushEvent = () => {
      if (pendingEventType === undefined && pendingDataLines.length === 0) return;
      const rawData = pendingDataLines.join("\n");
      let parsed: T | undefined;
      let raw: string | undefined;
      if (rawData.trim()) {
        try {
          parsed = JSON.parse(rawData) as T;
        } catch {
          raw = rawData;
        }
      }
      const evt: SSEEvent<T> = {};
      if (pendingEventType) evt.event = pendingEventType;
      if (parsed !== undefined) evt.data = parsed;
      if (raw !== undefined) evt.raw = raw;
      onEvent(evt);
      pendingEventType = undefined;
      pendingDataLines = [];
    };
 
    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      buf += decoder.decode(value, { stream: true });
      if (__SSE_DEBUG__) dbg("chunk", { len: value?.length ?? 0 });
 
      let idx: number;
      while ((idx = buf.indexOf("\n\n")) >= 0) {
        const rawEvent = buf.slice(0, idx);
        buf = buf.slice(idx + 2);
        const lines = rawEvent.split("\n");
        for (let line of lines) {
          if (line.endsWith("\r")) line = line.slice(0, -1);
          if (!line) continue;
          if (line.startsWith(":")) continue;
 
          const colon = line.indexOf(":");
          const field = colon === -1 ? line : line.slice(0, colon);
          let valueStr = colon === -1 ? "" : line.slice(colon + 1);
          if (valueStr.startsWith(" ")) valueStr = valueStr.slice(1);
          if (__SSE_DEBUG__) dbg("line", { field, valuePreview: valueStr.slice(0, 40) });
 
          if (field === "event") {
            pendingEventType = valueStr || undefined;
          } else if (field === "data") {
            pendingDataLines.push(valueStr);
          } else {
            // ignore other fields (id, retry...)
          }
        }
        flushEvent();
      }
    }
    if (aborted) {
      throw new DOMException("The operation was aborted.", "AbortError");
    }
  } finally {
    if (signal) {
      try {
        signal.removeEventListener("abort", onAbort as any);
      } catch {
        // ignore
      }
    }
    dbg("end");
  }
}
 
/**
 * 单一会话控制器：负责一次 SSE 连接的生命周期与订阅管理
 * - start(signal): 建立连接并解析事件流；仅允许调用一次，重复调用会先 stop
 * - subscribe(handler): 订阅 message/open/disconnect/reconnect 事件；返回 unsubscribe()
 * - stop(reason): 主动关闭（abort）连接并清理
 * - 心跳与断流检测：任意数据到达均视为心跳；若超出 idleTimeoutMs 未收到数据，则触发 disconnect 回调与事件
 * - 事件去抖：将密集到来的 message 聚合为批次（每 debounceMs 触发一次）
 */
export function createSSESession<T = any>(options: SSESessionOptions) {
  const {
    url,
    init,
    heartbeatMs = 5000,
    idleTimeoutMs = 10000,
    debounceMs = 50,
    onDisconnect,
    onReconnect,
  } = options;
  const controllerId = Math.random().toString(36).slice(2, 8);
  const dbg = (...args: any[]) => {
    if (__SSE_DEBUG__) {
      try { console.debug("[sse][session]", controllerId, ...args); } catch {}
    }
  };
  dbg("create", { url, heartbeatMs, idleTimeoutMs, debounceMs });
 
  // 订阅管理
  let nextSubId = 1;
  const subs = new Map<number, (ev: SSEControllerEvent<T>) => void>();
  const emit = (ev: SSEControllerEvent<T>) => {
    for (const [, h] of subs) {
      try { h(ev); } catch { /* 用户处理异常不影响主流程 */ }
    }
  };
 
  // 连接控制
  let controller: AbortController | null = null;
  let cleanupLink: (() => void) | null = null;
  let reader: ReadableStreamDefaultReader<Uint8Array> | null = null;
  let lastEventAt: number | undefined = undefined;
 
  // 去抖缓冲
  let debouncedTimer: any = null;
  const buffer: SSEEvent<T>[] = [];
  const flushBuffer = () => {
    if (buffer.length === 0) return;
    const items = buffer.splice(0, buffer.length);
    for (const evt of items) {
      emit({ type: "message", payload: evt });
    }
  };
 
  // 心跳/断流检测
  let idleTimer: any = null;
  const kickIdle = () => {
    // 收到任意数据即刷新
    lastEventAt = Date.now();
    if (idleTimer) clearTimeout(idleTimer);
    if (idleTimeoutMs > 0) {
      idleTimer = setTimeout(() => {
        // 超时未收到数据 -> 断流
        dbg("idle-timeout");
        emit({ type: "disconnect", reason: "idle-timeout" });
        try { onDisconnect?.({ lastEventAt, reason: "idle-timeout" }); } catch { /* no-op */ }
      }, idleTimeoutMs);
    }
  };
 
  const stop = (reason?: string) => {
    dbg("stop", { reason });
    try {
      if (debouncedTimer) { clearTimeout(debouncedTimer); debouncedTimer = null; }
      if (idleTimer) { clearTimeout(idleTimer); idleTimer = null; }
      if (reader) {
        try { reader.cancel(); } catch { /* ignore */ }
        reader = null;
      }
      if (cleanupLink) { try { cleanupLink(); } catch {} cleanupLink = null; }
      if (controller) { try { controller.abort(); } catch {} controller = null; }
    } finally {
      // 将残余缓冲 flush（可选，不 flush 也可）
      flushBuffer();
      if (reason) {
        emit({ type: "disconnect", reason });
        try { onDisconnect?.({ lastEventAt, reason }); } catch {}
      }
    }
  };
 
  const start = async (signal?: AbortSignal) => {
    // 若已存在连接，先关闭
    if (controller) stop("restart");
    dbg("start", { hasExternalSignal: !!signal });
 
    controller = new AbortController();
    if (signal) {
      cleanupLink = linkAbortSignals(controller, signal);
    }
 
    try {
      const resp = await fetch(url, { ...(init || {}), signal: controller.signal });
      if (!resp.ok) {
        // 非 2xx 直接作为 disconnect 信号抛出
        dbg("http-non-2xx", { status: resp.status });
        emit({ type: "disconnect", reason: `http-${resp.status}` });
        try { onDisconnect?.({ lastEventAt, reason: `http-${resp.status}` }); } catch {}
        return;
      }
      const ctype = resp.headers.get("Content-Type") || "";
      if (!ctype.includes("text/event-stream")) {
        // 非 SSE 响应：尝试整体解析一次，并以 message 形式抛出 raw
        const text = await resp.text();
        buffer.push({ raw: text });
        if (!debouncedTimer) {
          debouncedTimer = setTimeout(() => {
            debouncedTimer = null;
            flushBuffer();
          }, debounceMs);
        }
        // 视为一次完成后断开
        dbg("non-sse");
        emit({ type: "disconnect", reason: "non-sse" });
        try { onDisconnect?.({ lastEventAt, reason: "non-sse" }); } catch {}
        return;
      }
 
      emit({ type: "open" });
      dbg("open");
      lastEventAt = Date.now();
      kickIdle();
 
      reader = (resp.body as any)?.getReader?.() || null;
      if (!reader) {
        // 环境不支持 ReadableStream
        dbg("no-reader");
        emit({ type: "disconnect", reason: "no-reader" });
        try { onDisconnect?.({ lastEventAt, reason: "no-reader" }); } catch {}
        return;
      }
 
      // 通过 parseSSEStream 解析事件；在 onEvent 回调内做去抖聚合
      await parseSSEStream<any>(reader, (evt) => {
        // 收到任何事件刷新心跳
        kickIdle();
        // 容错策略：
        // - data 可为 JSON 对象（常规），若解析失败将以 raw 提供
        // - 如果出现重复/丢失 eventId 等，这里不做强校验，仅透传事件给上层
        if (__SSE_DEBUG__) dbg("evt", { hasData: "data" in evt && typeof (evt as any).data !== "undefined", hasRaw: typeof (evt as any).raw === "string" });
        buffer.push(evt as SSEEvent<T>);
        if (!debouncedTimer) {
          debouncedTimer = setTimeout(() => {
            debouncedTimer = null;
            flushBuffer();
          }, debounceMs);
        }
      }, controller!.signal);
 
      // 正常结束（对端完成）
      flushBuffer();
      dbg("disconnect", { reason: "eof" });
      emit({ type: "disconnect", reason: "eof" });
      try { onDisconnect?.({ lastEventAt, reason: "eof" }); } catch {}
    } catch (e: any) {
      // 连接或读取期间的异常：区分 abort 与其他
      if (e?.name === "AbortError") {
        // 主动停止或外部中止
        dbg("disconnect", { reason: "aborted" });
        emit({ type: "disconnect", reason: "aborted" });
        try { onDisconnect?.({ lastEventAt, reason: "aborted" }); } catch {}
      } else {
        // 网络/解析异常
        dbg("disconnect", { reason: "error" });
        emit({ type: "disconnect", reason: "error" });
        try { onDisconnect?.({ lastEventAt, reason: "error" }); } catch {}
      }
    } finally {
      if (idleTimer) { clearTimeout(idleTimer); idleTimer = null; }
      if (cleanupLink) { try { cleanupLink(); } catch {} cleanupLink = null; }
      if (controller) { controller = null; }
      if (reader) { reader = null; }
      dbg("finalized");
    }
  };
 
  const subscribe = (handler: (ev: SSEControllerEvent<T>) => void): Unsubscribe => {
    const id = nextSubId++;
    subs.set(id, handler);
    if (__SSE_DEBUG__) dbg("subscribe", { subId: id });
    return () => {
      subs.delete(id);
      if (__SSE_DEBUG__) dbg("unsubscribe", { subId: id });
    };
  };
 
  return {
    start,
    stop,
    subscribe,
    unsubscribe: (id: number) => subs.delete(id),
  };
}