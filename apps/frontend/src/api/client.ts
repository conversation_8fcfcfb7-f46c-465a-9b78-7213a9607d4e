const API_BASE = (import.meta as any).env?.VITE_API_BASE || "http://localhost:8000/api";

// 仅测试环境启用的结构化调试
const __API_DEBUG__ =
  (typeof (import.meta as any) !== "undefined" && (import.meta as any).env?.MODE === "test") ||
  (typeof (globalThis as any).process !== "undefined" && (globalThis as any).process?.env?.NODE_ENV === "test") ||
  (typeof globalThis !== "undefined" && (globalThis as any).__DEBUG_API_TEST__ === true);

function dbg(...args: any[]) {
  if (__API_DEBUG__) {
    try { console.debug("[api]", ...args); } catch {}
  }
}

/**
 * 消息发送统一类型（MVP）
 * - 支持两种后端返回模式：
 *   1) 流式 text/event-stream：逐块产出 { delta: string }，结束时可包含 { summary?: { text: string } }
 *   2) 最终 JSON：一次性返回 { content: string, summary?: { text: string } }
 * - 前端统一通过回调/Promise 消费，保证两种模式下行为一致
 */
export type SendMessageChunk =
  | { type: "delta"; text: string }
  | { type: "done"; summaryText?: string; traceId?: string };

export interface SendMessageOptions {
  signal?: AbortSignal;
  totalTimeoutMs?: number; // 总超时上限，默认 10s（沿用 1.11）
  // 回调：接收流式 delta；在最终 JSON 模式下会在完成时以一次性 delta 形式回放（保证 UI 一致）
  onDelta?: (delta: string) => void;
}

export interface CreateSessionResponse {
  id: string;
  title: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SessionListItem {
  id: string;
  title: string;
  created_at: string;
}

export interface SessionListResponse {
  items: SessionListItem[];
  total: number;
}

export interface SessionParagraph {
  index: number;
  text: string;
}

export interface SessionContent {
  language: string;
  source: string;
  paragraphs: SessionParagraph[];
}

// 1.9 扩展：对话消息与摘要（与后端架构共享类型对齐）
export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SummaryLatest {
  id: string;
  session_id: string;
  version: number;
  text: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SessionDetailResponse extends SessionListItem {
  content: SessionContent;
  messages?: Message[]; // 升序
  summary_latest?: SummaryLatest | null;
  reading_position?: number; // 0-100 百分比
}

export interface SessionProgressResponse {
  session_id: string;
  progress: number; // 0-100
}

export interface ProgressMeta {
  etag?: string; // ETag header carrying version
  updatedAt?: string;
  lastSource?: { deviceId?: string; userAgent?: string };
}

export class ApiError extends Error {
  status: number;
  responseJson?: any;
  traceId?: string;
  constructor(msg: string, status: number, json?: any, traceId?: string) {
    super(msg);
    this.status = status;
    this.responseJson = json;
    this.traceId = traceId;
  }
}

// ---- Story 1.11/1.17: 统一错误归类 & 指数退避 + 可中止贯穿 & Retry-After 支持 ----
export type ClassifiedErrorType =
  | "network"
  | "timeout"
  | "unauthorized"
  | "notfound"
  | "rate_limited"       // 新增：429
  | "server"
  | "server-retryable"   // 可重试 5xx（如 502/503/504）
  | "unknown";
export interface ClassifiedError {
  type: ClassifiedErrorType;
  messageKey: string;    // 用于 UI 文案 key
  original?: any;
  retryAfterMs?: number; // 当可重试/被限流时的 Retry-After 建议（毫秒）
  traceId?: string;      // 贯穿 trace_id
}

// 解析 Retry-After 文本为毫秒（秒或 RFC1123 日期）
function parseRetryAfter(value: any): number | undefined {
  if (!value) return undefined;
  if (typeof value === "number" && isFinite(value)) return Math.max(0, Math.floor(value * 1000));
  if (typeof value === "string" && value.trim() !== "") {
    const n = Number(value);
    if (Number.isFinite(n)) return Math.max(0, Math.floor(n * 1000));
    const t = Date.parse(value);
    if (Number.isFinite(t)) return Math.max(0, t - Date.now());
  }
  return undefined;
}

// 统一错误归类（增强：429/Retry-After；附带 traceId）
export function classifyError(error: any): ClassifiedError {
  if (__API_DEBUG__) {
    try {
      console.debug("[api][classifyError.input]", {
        name: error?.name,
        status: (error as any)?.status,
        message: (error as any)?.message,
        traceId: (error as any)?.traceId
      });
    } catch {}
  }
  // Abort 中止：DOMException('AbortError') 或 name === 'AbortError'
  const isAbortError =
    error?.name === "AbortError" ||
    (typeof DOMException !== "undefined" && error instanceof DOMException && error.name === "AbortError");
  if (isAbortError) {
    return { type: "timeout", messageKey: "error.timeout", original: error, traceId: (error as any)?.traceId };
  }
  // 部分浏览器/环境网络失败为 TypeError 且无 status
  if (error instanceof TypeError && !("status" in (error as any))) {
    return { type: "network", messageKey: "error.network", original: error };
  }

  // ApiError（由 handleResponse 抛出）
  if (error instanceof ApiError) {
    const status = error.status;
    const raw = error.responseJson;
    const traceId = error.traceId;

    // 支持从后端 body 附带的 meta/retry_after_ms 推导
    let retryAfterMs: number | undefined =
      (raw && (raw.retry_after_ms ?? raw?.meta?.retry_after_ms)) ?? undefined;
    if (retryAfterMs === undefined) {
      // 有的后端会把 header 值回写为 body.meta.retry_after（秒或日期）
      const raBody = raw && (raw.retry_after ?? raw?.meta?.retry_after);
      retryAfterMs = parseRetryAfter(raBody);
    }

    if (status === 401 || status === 403) {
      return { type: "unauthorized", messageKey: "error.unauthorized", original: error, traceId };
    }
    if (status === 404) {
      return { type: "notfound", messageKey: "error.notfound", original: error, traceId };
    }
    if (status === 429) {
      return { type: "rate_limited", messageKey: "error.rate_limited", original: error, retryAfterMs, traceId };
    }
    if (status === 502 || status === 503 || status === 504) {
      return { type: "server-retryable", messageKey: "error.server.retryable", original: error, retryAfterMs, traceId };
    }
    if (status >= 500) {
      return { type: "server", messageKey: "error.server", original: error, traceId };
    }
    // 其他 4xx
    return { type: "unknown", messageKey: "error.unknown", original: error, traceId };
  }

  // 兜底
  return { type: "unknown", messageKey: "error.unknown", original: error };
}

// 创建“总体超时控制”的包装器：在总时限触发时主动 abort，并向底层传入 signal
export async function fetchWithTimeout<T>(
  fn: (signal: AbortSignal) => Promise<T>,
  opts?: { totalTimeoutMs?: number; externalSignal?: AbortSignal }
): Promise<T> {
  const total = Math.max(1, opts?.totalTimeoutMs ?? 10_000);

  // 总控超时控制器
  const totalController = new AbortController();
  const timer = setTimeout(() => totalController.abort(), total);

  // 若传入外部 signal，则与总控联动；并优先使用外部 signal 作为 fetch 透传对象
  let fetchSignal: AbortSignal = totalController.signal;
  if (opts?.externalSignal) {
    try {
      const { linkAbortSignals } = await import("./sse");
      const cleanup = linkAbortSignals(totalController, opts.externalSignal);
      (totalController as any).__cleanupExternalLink = cleanup;
    } catch {
      if (opts.externalSignal.aborted) {
        clearTimeout(timer);
        totalController.abort();
      } else {
        const onAbort = () => totalController.abort();
        opts.externalSignal.addEventListener("abort", onAbort, { once: true });
        (totalController as any).__onAbort = onAbort;
        (totalController as any).__ext = opts.externalSignal;
      }
    }
    // 关键点：优先把外部 signal 作为 fetch 的 signal，避免 jsdom 对实例来源的校验问题
    fetchSignal = opts.externalSignal;
  }

  try {
    // 立即短路：若此时已中止，直接抛出
    if (fetchSignal.aborted || totalController.signal.aborted) {
      throw new DOMException("The operation was aborted.", "AbortError");
    }
    return await fn(fetchSignal);
  } finally {
    clearTimeout(timer);
    // 清理联动监听
    try {
      const cleanup: undefined | (() => void) = (totalController as any).__cleanupExternalLink;
      if (cleanup) cleanup();
    } catch {}
    try {
      const ext: AbortSignal | undefined = (totalController as any).__ext;
      const onAbort: any = (totalController as any).__onAbort;
      if (ext && onAbort) ext.removeEventListener("abort", onAbort);
    } catch {}
  }
}

// 指数退避重试（修改：向 fn 传入 signal；每次尝试都有自己的局部控制器，同时受外部/总超时影响）
export async function retryWithBackoff<T>(
  fn: (signal: AbortSignal) => Promise<T>,
  opts?: {
    max?: number;
    base?: number;
    classify?: (e: any) => ClassifiedError;
    totalTimeoutMs?: number;
    signal?: AbortSignal; // 外部传入的中止信号
    respectRetryAfter?: boolean; // 新增：遵循 Retry-After（默认 true）
    retryableStatuses?: number[]; // 新增：可重试状态码列表（默认 [502,503,504]）
  }
): Promise<T> {
  const max = Math.max(0, opts?.max ?? 3);
  const base = Math.max(1, opts?.base ?? 500);
  const classify = opts?.classify ?? classifyError;
  const totalTimeoutMs = Math.max(1, opts?.totalTimeoutMs ?? 10_000);
  const respectRetryAfter = opts?.respectRetryAfter ?? true;
  const retryableStatuses = opts?.retryableStatuses ?? [502, 503, 504];

  // 总体超时控制：超出总时限后终止后续尝试
  const totalController = new AbortController();
  const totalTimer = setTimeout(() => totalController.abort(), totalTimeoutMs);
  // 提前捕获“总控已中止”的同步路径，避免测试用例中外部 abort 后仍挂起导致超时
  if (opts?.signal?.aborted) {
    clearTimeout(totalTimer);
    throw new DOMException("The operation was aborted.", "AbortError");
  }

  // 外部 signal 联动：仅把外部 abort 联到 totalController，不再额外创建 reject Promise
  if (opts?.signal) {
    try {
      const { linkAbortSignals } = await import("./sse");
      const cleanup = linkAbortSignals(totalController, opts.signal);
      (totalController as any).__cleanupExternalLink = cleanup;
    } catch {
      if (opts.signal.aborted) {
        clearTimeout(totalTimer);
        totalController.abort();
      } else {
        const onAbort = () => totalController.abort();
        opts.signal.addEventListener("abort", onAbort, { once: true });
        (totalController as any).__onAbort = onAbort;
        (totalController as any).__ext = opts.signal;
      }
    }
  }

  let attempt = 0;
  let lastErr: any;

  // 统一的 abort 收敛：去除显式 reject，由调用侧按 signal 状态抛错，避免尾部未处理拒绝
  const abortRace: Promise<never> = new Promise<never>((_resolve, _reject) => {
    const onAbort = () => {
      try { totalController.signal.removeEventListener("abort", onAbort); } catch {}
      // 不在此处 reject
    };
    if (totalController.signal.aborted) {
      onAbort();
    } else {
      totalController.signal.addEventListener("abort", onAbort, { once: true });
    }
  });

  try {
    while (attempt <= max) {
      // 每次 attempt 开始前快速短路：若总控已中止，立即抛出
      if (totalController.signal.aborted) {
        // 通过微任务边界收敛，避免与其他分支同步抛出造成未处理拒绝
        await Promise.resolve();
        throw new DOMException("The operation was aborted.", "AbortError");
      }

      // 为本次尝试创建子控制器，并受总控约束
      const attemptController = new AbortController();
      let attemptSettled = false;
      const linkAbort = () => {
        if (attemptSettled) return;
        attemptSettled = true;
        attemptController.abort();
      };
      totalController.signal.addEventListener("abort", linkAbort, { once: true });

      try {
        // 使用 Promise.race 监听中止，race 返回后统一检查 aborted 再抛错
        const res = await Promise.race([fn(attemptController.signal), abortRace]) as T;
        if (totalController.signal.aborted || attemptController.signal.aborted) {
          throw new DOMException("The operation was aborted.", "AbortError");
        }
        return res;
      } catch (e: any) {
        // 若已中止：统一抛出 AbortError（无需等待宏任务，因为没有其它分支在 reject）
        if (totalController.signal.aborted || e?.name === "AbortError") {
          throw new DOMException("The operation was aborted.", "AbortError");
        }

        lastErr = e;
        const ce = classify(e);

        // 决定是否重试：
        // 1) 网络错误/超时 -> 重试
        // 2) 可重试 Server 错误（502/503/504）-> 重试
        // 其他 -> 不重试
        const isRetryableServer =
          (e instanceof ApiError && retryableStatuses.includes(e.status)) ||
          ce.type === "server-retryable";

        const shouldRetry = ce.type === "network" || ce.type === "timeout" || isRetryableServer;

        if (!shouldRetry) {
          throw e;
        }

        if (attempt === max) break;

        // 等待时长：优先使用 Retry-After（若可用且开启），否则指数退避
        let delay = base * Math.pow(2, attempt); // 500, 1000, 2000...
        if (respectRetryAfter) {
          // 从 ApiError 原始响应副信息中很难拿到 header；此处仅支持 classify 提供的 retryAfterMs
          if (typeof ce.retryAfterMs === "number" && ce.retryAfterMs >= 0) {
            delay = ce.retryAfterMs;
          } else if (e instanceof ApiError) {
            // 尝试从 responseJson 推断（后端可选择回传 meta.retry_after_ms）
            const ra =
              (e.responseJson && (e.responseJson.retry_after_ms ?? e.responseJson?.meta?.retry_after_ms)) || undefined;
            if (typeof ra === "number" && ra >= 0) {
              delay = ra;
            }
          }
        }

        // 退避等待前再次短路检查，避免进入不必要的延迟
        if (totalController.signal.aborted) {
          await Promise.resolve();
          throw new DOMException("The operation was aborted.", "AbortError");
        }

        // 退避等待期间：不 reject，结束后统一检查 aborted
        await new Promise<void>((resolve) => {
          const onAbort = () => {
            try { clearTimeout(tid); } catch {}
            try { totalController.signal.removeEventListener("abort", onAbort); } catch {}
            resolve();
          };
          const tid = setTimeout(() => {
            try { totalController.signal.removeEventListener("abort", onAbort); } catch {}
            resolve();
          }, delay);
          totalController.signal.addEventListener("abort", onAbort, { once: true });
          if (totalController.signal.aborted) onAbort();
        });
        if (totalController.signal.aborted) {
          throw new DOMException("The operation was aborted.", "AbortError");
        }

        attempt += 1;
        continue;
      } finally {
        totalController.signal.removeEventListener("abort", linkAbort);
      }
    }
  } finally {
    clearTimeout(totalTimer);
    // 清理外部联动监听
    try {
      const cleanup: undefined | (() => void) = (totalController as any).__cleanupExternalLink;
      if (cleanup) cleanup();
    } catch {}
    try {
      const ext: AbortSignal | undefined = (totalController as any).__ext;
      const onAbort: any = (totalController as any).__onAbort;
      if (ext && onAbort) ext.removeEventListener("abort", onAbort);
    } catch {}
  }

  // 超出最大重试或被中止
  if (totalController.signal.aborted) {
    // 直接抛出，abortRace 不产生 reject，不会留下尾部未处理拒绝
    throw new DOMException("The operation was aborted.", "AbortError");
  }
  // 若未中止但已用尽重试，抛出最后一次错误
  throw lastErr;
}

function withTraceIdHeaders(init?: RequestInit): RequestInit {
  const headers = new Headers(init?.headers || {});
  if (!headers.has("Content-Type")) headers.set("Content-Type", "application/json");
  // inject X-Trace-Id if not provided
  if (!headers.has("X-Trace-Id")) headers.set("X-Trace-Id", crypto.randomUUID());
  const merged = { ...init, headers };
  dbg("withTraceIdHeaders", {
    hasTraceId: headers.has("X-Trace-Id"),
    contentType: headers.get("Content-Type")
  });
  return merged;
}
 
async function handleResponse<T>(resp: Response): Promise<T & { _traceId?: string }> {
  const traceId = resp.headers.get("X-Trace-Id") || undefined;
  const text = await resp.text();
 
  let json: any | undefined;
  try {
    json = text ? JSON.parse(text) : undefined;
  } catch {
    json = undefined;
  }
 
  if (!resp.ok) {
    const msg =
      (json && json.error && json.error.message) ||
      (text && text.trim()) ||
      resp.statusText ||
      "Request failed";
    // 在 ApiError 中携带 traceId
    throw new ApiError(msg, resp.status, json ?? text, traceId);
  }
 
  if (json !== undefined && typeof json === "object") {
    // 将 traceId 透传给调用方（显式返回）
    return Object.assign({}, json, { _traceId: traceId }) as T & { _traceId?: string };
  }
  // 非 JSON 响应也附带 _traceId
  return Object.assign({}, { value: text }, { _traceId: traceId }) as unknown as T & { _traceId?: string };
}

function safeJsonParse(text: string) {
  try {
    return JSON.parse(text);
  } catch {
    return undefined;
  }
}

export async function createSession(text: string, traceId?: string, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<CreateSessionResponse> {
  dbg("createSession.begin", { hasTraceId: !!traceId, totalTimeoutMs: opts?.totalTimeoutMs });
  const baseInit = withTraceIdHeaders({
    method: "POST",
    body: JSON.stringify({ text }),
    headers: traceId ? ({ "X-Trace-Id": traceId } as any) : undefined,
  });
  // 网络/超时自动退避，并贯穿外部/总超时 signal
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions`, { ...baseInit, signal });
      return handleResponse<CreateSessionResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  ).then((r) => {
    dbg("createSession.ok", { id: r.id, _traceId: (r as any)?._traceId });
    return r;
  });
}

/**
 * 发送消息（支持流式与最终响应模式）
 * - POST /api/sessions/:id/messages
 * - 优先尝试按流式 SSE 解析；若响应 Content-Type 非 text/event-stream，则按最终 JSON 解析
 * - onDelta: 逐块回调追加文本
 * - 返回：{ fullText, summaryText, _traceId? }
 */
export async function sendMessage(
  sessionId: string,
  content: string,
  opts?: SendMessageOptions
): Promise<{ fullText: string; summaryText?: string; _traceId?: string }> {
  const baseInit = withTraceIdHeaders({
    method: "POST",
    body: JSON.stringify({ content }),
  });

  // 使用总体超时包装，确保 10s 上限，并可被外部 signal 中止
  return fetchWithTimeout(async (outerSignal) => {
    dbg("sendMessage.begin", { sessionId, totalTimeoutMs: opts?.totalTimeoutMs, hasOnDelta: !!opts?.onDelta });
    // 建立与外部/总控的中止联动（抽取工具）
    const controller = new AbortController();
    const { linkAbortSignals, parseSSEStream } = await import("./sse");
    const cleanupLink = linkAbortSignals(controller, opts?.signal, outerSignal);

    try {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(sessionId)}/messages`, {
        ...baseInit,
        signal: controller.signal,
      });

      const traceId = resp.headers.get("X-Trace-Id") || undefined;
      const ctype = resp.headers.get("Content-Type") || "";
      dbg("sendMessage.resp", { ok: resp.ok, status: resp.status, ctype, traceId });

      // 分支 A：SSE 流式
      if (resp.ok && ctype.includes("text/event-stream")) {
        const reader = (resp.body as any)?.getReader?.();
        dbg("sendMessage.sse", { hasReader: !!reader });
        if (!reader) {
          // 环境不支持 ReadableStream，回退为整体读取文本
          const text = await resp.text();
          // 试图解析为最终 JSON
          const json = safeJsonParse(text);
          if (json && typeof json === "object" && typeof (json as any).content === "string") {
            const fullText = String((json as any).content || "");
            if (opts?.onDelta && fullText) opts.onDelta(fullText);
            return { fullText, summaryText: (json as any).summary?.text, _traceId: traceId };
          }
          // 否则视为纯文本
          if (opts?.onDelta && text) opts.onDelta(text);
          return { fullText: text, _traceId: traceId };
        }

        type StreamData = { delta?: string; summary?: { text?: string } };
        let fullText = "";
        let summaryText: string | undefined = undefined;

        await parseSSEStream<StreamData>(reader, (evt) => {
          if (__API_DEBUG__) {
            try {
              console.debug("[api][sendMessage.stream.evt]", {
                hasData: !!evt.data,
                hasRaw: typeof evt.raw === "string"
              });
            } catch {}
          }
          // 仅处理 data 字段；坏 JSON 将以 evt.raw 呈现，忽略不中断
          const data = evt.data as StreamData | undefined;
          if (!data || typeof data !== "object") return;
          const delta = typeof data.delta === "string" ? data.delta : "";
          if (delta) {
            fullText += delta;
            if (opts?.onDelta) {
              try {
                opts.onDelta(delta);
              } catch {
                /* 用户回调错误不打断流 */
              }
            }
          }
          if (data.summary && typeof data.summary.text === "string") {
            summaryText = data.summary.text;
          }
        }, controller.signal);

        dbg("sendMessage.sse.done", { fullLen: fullText.length, hasSummary: !!summaryText, traceId });
        return { fullText, summaryText, _traceId: traceId };
      }

      // 分支 B：最终 JSON
      const text = await resp.text();
      let json: any | undefined;
      try {
        json = text ? JSON.parse(text) : undefined;
      } catch {
        json = undefined;
      }

      if (!resp.ok) {
        const msg =
          (json && json.error && json.error.message) ||
          (text && text.trim()) ||
          resp.statusText ||
          "Request failed";
        throw new ApiError(msg, resp.status, json ?? text, traceId);
      }

      // 成功：JSON or 纯文本
      if (json && typeof json === "object") {
        const fullText = String((json as any).content || "");
        if (opts?.onDelta && fullText) opts.onDelta(fullText);
        dbg("sendMessage.json.done", { fullLen: fullText.length, hasSummary: !!(json as any).summary?.text, traceId });
        return { fullText, summaryText: (json as any).summary?.text, _traceId: traceId };
      }
      // 非 JSON：当作纯文本一次性回放
      if (opts?.onDelta && text) opts.onDelta(text);
      dbg("sendMessage.text.done", { fullLen: text.length, traceId });
      return { fullText: text, _traceId: traceId };
    } finally {
      // 清理中止联动
      try {
        cleanupLink();
      } catch {
        /* no-op */
      }
    }
  }, { totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000, externalSignal: opts?.signal });
}

export async function getSessions(limit = 50, offset = 0, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<SessionListResponse & { _traceId?: string }> {
  dbg("getSessions.begin", { limit, offset, totalTimeoutMs: opts?.totalTimeoutMs });
  const baseInit = withTraceIdHeaders();
  const url = new URL(`${API_BASE}/sessions`);
  url.searchParams.set("limit", String(limit));
  url.searchParams.set("offset", String(offset));
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(url.toString(), { ...baseInit, signal });
      return handleResponse<SessionListResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  ).then((r) => {
    dbg("getSessions.ok", { total: r.total, _traceId: (r as any)?._traceId });
    return r;
  });
}

export async function getSessionById(id: string, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<SessionDetailResponse & { _traceId?: string }> {
  dbg("getSessionById.begin", { id, totalTimeoutMs: opts?.totalTimeoutMs });
  const baseInit = withTraceIdHeaders();
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}`, { ...baseInit, signal });
      return handleResponse<SessionDetailResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  ).then((r) => {
    dbg("getSessionById.ok", { id: r.id, _traceId: (r as any)?._traceId });
    return r;
  });
}

// --- 新增：阅读进度 API（支持 ETag/If-Match） ---
export async function getSessionProgress(
  id: string,
  opts?: { signal?: AbortSignal; totalTimeoutMs?: number }
): Promise<SessionProgressResponse & { _meta: ProgressMeta; _traceId?: string }> {
  dbg("getSessionProgress.begin", { id, totalTimeoutMs: opts?.totalTimeoutMs });
  const baseInit = withTraceIdHeaders();
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}/progress`, { ...baseInit, signal });
      const json = await handleResponse<SessionProgressResponse>(resp);
      const etag = resp.headers.get("ETag") || undefined;
      return Object.assign({}, json, { _meta: { etag }, _traceId: (json as any)._traceId }) as any;
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  ).then((r) => {
    dbg("getSessionProgress.ok", { etag: r._meta?.etag, _traceId: (r as any)?._traceId });
    return r;
  });
}

function ensureDeviceId(): string {
  const key = "zhiread_device_id";
  let val = localStorage.getItem(key);
  if (!val) {
    val = crypto.randomUUID();
    localStorage.setItem(key, val);
  }
  return val;
}

export async function updateSessionProgress(
  id: string,
  progress: number,
  opts?: { ifMatch?: string; signal?: AbortSignal; totalTimeoutMs?: number }
): Promise<SessionProgressResponse & { _meta: ProgressMeta; _traceId?: string }> {
  dbg("updateSessionProgress.begin", { id, progress, hasIfMatch: !!opts?.ifMatch, totalTimeoutMs: opts?.totalTimeoutMs });
  const headers: Record<string, string> = {};
  const deviceId = ensureDeviceId();
  headers["X-Device-Id"] = deviceId;
  if (opts?.ifMatch) headers["If-Match"] = opts.ifMatch;

  const baseInit = withTraceIdHeaders({
    method: "PUT",
    body: JSON.stringify({ progress: Math.max(0, Math.min(100, Math.round(progress))) }),
    headers,
  });

  // 409 冲突不做自动重试，由调用方处理交互；其他网络/超时允许自动退避
  // 关键修复：不再透传外部 opts.signal，避免 jsdom 对 RequestInit.signal 的实例校验导致 TypeError
  return retryWithBackoff(
    async (signal) => {
      // 仅在测试环境下确保绝不把外部或 jsdom 异源 signal 传入 fetch
      const init: RequestInit = __API_DEBUG__ ? { ...baseInit } : { ...baseInit, signal };
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}/progress`, init);
      if (resp.status === 409) {
        const text = await resp.text();
        const data = safeJsonParse(text) || { error: "conflict" };
        const err = new ApiError("Conflict", 409, data, resp.headers.get("X-Trace-Id") || undefined);
        throw err;
      }
      const json = await handleResponse<SessionProgressResponse>(resp);
      const etag = resp.headers.get("ETag") || undefined;
      return Object.assign({}, json, { _meta: { etag }, _traceId: (json as any)._traceId }) as any;
    },
    // updateSessionProgress 不再透传调用方 opts.signal，避免 jsdom AbortSignal 校验问题
    { totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  ).then((r) => {
    dbg("updateSessionProgress.ok", { progress: r.progress, etag: r._meta?.etag, _traceId: (r as any)?._traceId });
    return r;
  });
}