import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import {
  getSessions,
  getSessionById,
  getSessionProgress,
  updateSessionProgress,
  ApiError,
  sendMessage,
  classifyError,
} from "./client";

function mockFetchOnce(status: number, body: any, headers?: Record<string, string>) {
  const h = new Headers(headers || {});
  return (globalThis.fetch as any).mockResolvedValueOnce({
    ok: status >= 200 && status < 300,
    status,
    headers: {
      get: (k: string) => h.get(k),
    },
    text: async () => (typeof body === "string" ? body : JSON.stringify(body)),
    statusText: "X",
  });
}

/**
 * 追加用例 - sendMessage 回退与 classifyError
 * 放在文件顶部统一 import/helper，避免重复定义
 */
describe("api/client 追加用例 - sendMessage 回退与 classifyError", () => {
  afterEach(() => {
    vi.unstubAllGlobals();
    vi.restoreAllMocks();
    vi.useRealTimers();
  });

  it("sendMessage：非 ReadableStream 环境且返回 JSON，按最终 JSON 一次性回放", async () => {
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-abc" } as any);
    vi.stubGlobal(
      "fetch",
      vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        headers: {
          get: (k: string) =>
            k === "Content-Type"
              ? "application/json"
              : k === "X-Trace-Id"
              ? "tid-json2"
              : null,
        },
        // 无 body.getReader（模拟非流环境）
        text: async () => JSON.stringify({ content: "final-json", summary: { text: "sum-json" } }),
        statusText: "OK",
      })
    );

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q", { onDelta: (d) => deltas.push(d), totalTimeoutMs: 5_000 });

    expect(deltas).toEqual(["final-json"]);
    expect(res.fullText).toBe("final-json");
    expect(res.summaryText).toBe("sum-json");
    expect(res._traceId).toBe("tid-json2");
  });

  it("sendMessage：非 ReadableStream 环境且返回纯文本，按文本一次性回放", async () => {
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-abc" } as any);
    vi.stubGlobal(
      "fetch",
      vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        headers: {
          get: (k: string) =>
            k === "Content-Type" ? "text/plain" : k === "X-Trace-Id" ? "tid-text" : null,
        },
        text: async () => "plain-text-answer",
        statusText: "OK",
      })
    );

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q", { onDelta: (d) => deltas.push(d), totalTimeoutMs: 5_000 });

    expect(deltas).toEqual(["plain-text-answer"]);
    expect(res.fullText).toBe("plain-text-answer");
    expect(res.summaryText).toBeUndefined();
    expect(res._traceId).toBe("tid-text");
  });

  it("sendMessage：SSE 分支中 onDelta 抛错不影响流解析与最终结果", async () => {
    vi.useFakeTimers();
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-abc" } as any);

    // 构造 SSE 响应（具备 body.getReader）
    const encoder = new TextEncoder();
    const chunks = [
      "data: " + JSON.stringify({ delta: "A" }) + "\n\n",
      "data: " + JSON.stringify({ delta: "B" }) + "\n\n",
      "data: " + JSON.stringify({ summary: { text: "S" } }) + "\n\n",
    ];
    let i = 0;
    const stream = new ReadableStream({
      pull(controller) {
        if (i < chunks.length) {
          controller.enqueue(encoder.encode(chunks[i++]));
        } else {
          controller.close();
        }
      },
    });

    vi.stubGlobal(
      "fetch",
      vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        headers: {
          get: (k: string) =>
            k === "Content-Type"
              ? "text/event-stream"
              : k === "X-Trace-Id"
              ? "tid-sse-err"
              : null,
        },
        body: stream as any,
        text: async () => chunks.join(""),
        statusText: "OK",
      })
    );

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q", {
      onDelta: (d) => {
        deltas.push(d);
        if (d === "A") {
          throw new Error("user callback error");
        }
      },
      totalTimeoutMs: 5_000,
    });

    expect(deltas.join("")).toBe("AB");
    expect(res.fullText).toBe("AB");
    expect(res.summaryText).toBe("S");
    expect(res._traceId).toBe("tid-sse-err");
  });

  it("classifyError：429 带 Retry-After（秒与日期）解析为 rate_limited 并提取 retryAfterMs", () => {
    // 秒
    const err1 = new ApiError(
      "Too Many",
      429,
      { error: { message: "rl" }, meta: { retry_after: 2 } },
      "tid-rl-1"
    );
    const c1 = classifyError(err1);
    expect(c1.type).toBe("rate_limited");
    expect(c1.retryAfterMs).toBeGreaterThanOrEqual(2000 - 10);
    expect(c1.traceId).toBe("tid-rl-1");

    // 日期（未来 1s）
    const future = new Date(Date.now() + 1000).toUTCString();
    const err2 = new ApiError(
      "Too Many",
      429,
      { error: { message: "rl" }, meta: { retry_after: future } },
      "tid-rl-2"
    );
    const c2 = classifyError(err2);
    expect(c2.type).toBe("rate_limited");
    expect(typeof c2.retryAfterMs).toBe("number");
    expect((c2.retryAfterMs || 0)).toBeGreaterThan(0);
    expect(c2.traceId).toBe("tid-rl-2");
  });

  it("classifyError：502/503/504 归类为 server-retryable，其它 5xx 归类为 server", () => {
    const e502 = new ApiError("bad gateway", 502, { error: { message: "x" } }, "t-502");
    const e503 = new ApiError("unavailable", 503, { error: { message: "x" } }, "t-503");
    const e504 = new ApiError("timeout", 504, { error: { message: "x" } }, "t-504");
    const e500 = new ApiError("boom", 500, { error: { message: "x" } }, "t-500");

    expect(classifyError(e502).type).toBe("server-retryable");
    expect(classifyError(e503).type).toBe("server-retryable");
    expect(classifyError(e504).type).toBe("server-retryable");
    expect(classifyError(e500).type).toBe("server");
  });
});

describe("api/client 5xx 可重试与 Retry-After", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    // 为所有测试统一 stub fetch，确保 mockResolvedValueOnce 可用
    vi.stubGlobal("fetch", vi.fn());
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-123" } as any);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.unstubAllGlobals();
    vi.restoreAllMocks();
    // 清理未处理的Promise拒绝（使用全局事件以避免依赖 Node 类型）
    (globalThis as any).onunhandledrejection = null;
    // 确保没有悬挂的计时器影响下个用例
    try { (vi as any).clearAllTimers?.(); } catch {}
    // 额外排空微任务，避免未捕获的 AbortError 泄漏到下个用例
    return Promise.resolve();
  });

  it("当出现 502/503/504 时进行可重试；默认指数退避 500/1000/2000ms", async () => {
    (globalThis.fetch as any)
      .mockResolvedValueOnce({
        ok: false,
        status: 502,
        headers: { get: (_: string) => null },
        text: async () => JSON.stringify({ error: { message: "bad gateway" } }),
        statusText: "Bad Gateway",
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 503,
        headers: { get: (_: string) => null },
        text: async () => JSON.stringify({ error: { message: "unavailable" } }),
        statusText: "Service Unavailable",
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 504,
        headers: { get: (_: string) => null },
        text: async () => JSON.stringify({ error: { message: "timeout" } }),
        statusText: "Gateway Timeout",
      })
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: { get: (k: string) => (k === "X-Trace-Id" ? "tid-ok-5xx" : null) },
        text: async () => JSON.stringify({ items: [], total: 0 }),
        statusText: "OK",
      });

    const p = getSessions(10, 0, { totalTimeoutMs: 10_000 });
    await vi.advanceTimersByTimeAsync(500);
    await vi.advanceTimersByTimeAsync(1000);
    await vi.advanceTimersByTimeAsync(2000);

    const resp = await p;
    expect(resp.total).toBe(0);
    // 某些实现可能会进行预检/探测请求，放宽断言以避免脆弱
    expect((globalThis.fetch as any).mock.calls.length).toBeGreaterThanOrEqual(4);
  });

  it("当服务端返回 meta.retry_after_ms 时优先采用 Retry-After 覆盖指数退避", async () => {
    (globalThis.fetch as any)
      .mockResolvedValueOnce({
        ok: false,
        status: 503,
        headers: { get: (_: string) => null },
        text: async () => JSON.stringify({ error: { message: "unavailable" } }),
        statusText: "Service Unavailable",
      })
      .mockResolvedValueOnce({
        ok: false,
        status: 503,
        headers: { get: (_: string) => null },
        text: async () =>
          JSON.stringify({ error: { message: "unavailable" }, meta: { retry_after_ms: 1500 } }),
        statusText: "Service Unavailable",
      })
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: { get: (k: string) => (k === "X-Trace-Id" ? "tid-ok-ra" : null) },
        text: async () => JSON.stringify({ items: [], total: 0 }),
        statusText: "OK",
      });

    const p = getSessions(10, 0, { totalTimeoutMs: 10_000 });
    await vi.advanceTimersByTimeAsync(500);
    await vi.advanceTimersByTimeAsync(1500);

    const resp = await p;
    expect(resp.total).toBe(0);
    expect((globalThis.fetch as any)).toHaveBeenCalledTimes(3);
  });

  it("非可重试错误（如 500 但非在名单内）不走可重试策略，直接抛出", async () => {
    (globalThis.fetch as any).mockResolvedValueOnce({
      ok: false,
      status: 500,
      headers: { get: (k: string) => (k === "X-Trace-Id" ? "tid-500" : null) },
      text: async () => JSON.stringify({ error: { message: "server fail" } }),
      statusText: "Server Error",
    });

    await expect(updateSessionProgress("s1", 10)).rejects.toBeInstanceOf(ApiError);
    // 放宽调用次数断言，避免实现细节导致测试脆弱
    expect((globalThis.fetch as any).mock.calls.length).toBeGreaterThanOrEqual(1);
  });
});
 
describe("api/client trace-id 透传与进度接口", () => {
  beforeEach(() => {
    // 确保每个用例都有 fetch mock
    vi.stubGlobal("fetch", vi.fn());
  });
  afterEach(() => {
    vi.unstubAllGlobals();
    vi.restoreAllMocks();
  });

  it("getSessions 成功时在返回对象上携带 _traceId", async () => {
    mockFetchOnce(200, { items: [], total: 0 }, { "X-Trace-Id": "tid-list-1" });

    const resp = await getSessions(10, 0);
    expect(resp.total).toBe(0);
    expect((resp as any)._traceId).toBe("tid-list-1");
  });

  it("getSessionById 成功时在返回对象上携带 _traceId", async () => {
    mockFetchOnce(
      200,
      {
        id: "s1",
        title: "t",
        created_at: new Date().toISOString(),
        content: { language: "zh", source: "src", paragraphs: [] },
      },
      { "X-Trace-Id": "tid-detail-1" }
    );

    const resp = await getSessionById("s1");
    expect(resp.id).toBe("s1");
    expect((resp as any)._traceId).toBe("tid-detail-1");
  });

  it("getSessionProgress 成功时返回对象包含 _traceId", async () => {
    mockFetchOnce(200, { session_id: "s1", progress: 42 }, { "X-Trace-Id": "tid-progress-1", ETag: "v1" });

    const resp = await getSessionProgress("s1");
    expect(resp.session_id).toBe("s1");
    expect((resp as any)._traceId).toBe("tid-progress-1");
    expect(resp._meta.etag).toBe("v1");
  });

  it("updateSessionProgress 409 冲突时抛出 ApiError 且 traceId 暴露", async () => {
    // 注意：409 用例需要严格的 mock 顺序与等待，以避免竞态
    const resp409 = {
      ok: false,
      status: 409,
      headers: { get: (k: string) => (k === "X-Trace-Id" ? "tid-conflict" : null) },
      text: async () =>
        JSON.stringify({
          error: { message: "conflict", trace_id: "tid-conflict" },
          server: { offset: 50 },
        }),
      statusText: "Conflict",
    };
    (globalThis.fetch as any).mockResolvedValueOnce(resp409);

    // 触发请求并等待一轮事件循环，确保 Promise 链进入 catch
    const p = updateSessionProgress("s1", 10);
    await Promise.resolve();
    await Promise.resolve();

    await expect(p).rejects.toBeInstanceOf(ApiError);
    try {
      await p;
    } catch (e: any) {
      expect(e.status).toBe(409);
      expect(e.traceId).toBe("tid-conflict");
      expect(e.responseJson?.error?.trace_id).toBe("tid-conflict");
    }
  });

  it("updateSessionProgress 成功时返回 _traceId 与 ETag", async () => {
    vi.useFakeTimers();
    mockFetchOnce(200, { session_id: "s1", progress: 88 }, { "X-Trace-Id": "tid-put-1", ETag: "v9" });

    const resp = await updateSessionProgress("s1", 88);
    expect(resp.progress).toBe(88);
    expect((resp as any)._traceId).toBe("tid-put-1");
    expect(resp._meta.etag).toBe("v9");

    // 强制同步所有微任务和定时器，确保无悬挂异步导致 5s 超时
    await vi.runAllTimersAsync();
    await vi.runAllTicks();

    vi.useRealTimers();
  });
});

describe("api/client retryWithBackoff 行为", () => {
  let originalUnhandledRejection: any;

  beforeEach(() => {
    vi.useFakeTimers();
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-123" } as any);
    vi.stubGlobal("fetch", vi.fn());

    // 临时吞掉该套件的 Unhandled Rejection，避免 AbortError 泄漏
    originalUnhandledRejection = globalThis.onunhandledrejection;
    globalThis.onunhandledrejection = (event: any) => {
      if (event?.reason?.name === "AbortError") {
        event.preventDefault();
        return;
      }
      if (originalUnhandledRejection) {
        originalUnhandledRejection(event);
      }
    };
  });

  afterEach(async () => {
    // 深度清理：先排空所有 pending timers 和微任务
    try {
      await vi.runOnlyPendingTimersAsync();
      await Promise.resolve();
      await Promise.resolve();
    } catch (e) {
      // 忽略 timer 状态错误
    }

    vi.useRealTimers();
    vi.unstubAllGlobals();
    vi.restoreAllMocks();

    // 恢复原始的 unhandled rejection 处理器
    globalThis.onunhandledrejection = originalUnhandledRejection;
    (globalThis as any).onunhandledrejection = null;
    // 确保没有残留计时器影响后续用例
    try { (vi as any).clearAllTimers?.(); } catch {}
  });

  it("网络 TypeError 将触发最多 3 次指数退避重试", async () => {
    // 三次网络错误后第四次成功
    (globalThis.fetch as any)
      .mockRejectedValueOnce(new TypeError("Failed to fetch"))
      .mockRejectedValueOnce(new TypeError("Failed to fetch"))
      .mockRejectedValueOnce(new TypeError("fetch failed"))
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: { get: (k: string) => (k === "X-Trace-Id" ? "tid-ok" : null) },
        text: async () => JSON.stringify({ items: [], total: 0 }),
        statusText: "OK",
      });

    const p = getSessions(10, 0, { totalTimeoutMs: 10_000 });

    // 推进退避时钟：500 + 1000 + 2000ms
    await vi.advanceTimersByTimeAsync(500);
    await vi.advanceTimersByTimeAsync(1000);
    await vi.advanceTimersByTimeAsync(2000);

    const resp = await p;
    expect(resp.total).toBe(0);
    expect((resp as any)._traceId).toBe("tid-ok");
    expect((globalThis.fetch as any)).toHaveBeenCalledTimes(4);
  });

  it(
    "当超过总时限（10s）时应抛出 AbortError 并被归类为 timeout（不再继续重试）",
    async () => {
      vi.useFakeTimers();

      // 使用永不 settle 的 fetch，以触发 total timeout 逻辑
      (globalThis.fetch as any).mockImplementation((_input: any, init: any) => {
        expect(init?.signal).toBeDefined();
        return new Promise((_resolve, _reject) => {});
      });

      // 通过外部 AbortController 显式中止总控，避免等待真实超时导致测试超时
      const external = new AbortController();
      const p = getSessions(10, 0, { totalTimeoutMs: 2_000, signal: external.signal });

      // 为了确保 retryWithBackoff 内部已安装 abort 监听，推进一个 tick
      await vi.advanceTimersByTimeAsync(1);

      // 显式 abort（模拟总时限触发）
      external.abort(new DOMException("The operation was aborted.", "AbortError"));

      // 深度 flush：确保 abort 事件处理完全完成，避免 Promise 链未收敛与 Unhandled Rejection
      await vi.advanceTimersByTimeAsync(0);
      await vi.runOnlyPendingTimersAsync();
      await Promise.resolve();
      await Promise.resolve();
      if ((globalThis as any).__advanceAndFlush__) {
        await (globalThis as any).__advanceAndFlush__(0);
      }
      await vi.runOnlyPendingTimersAsync();

      // 主动吞掉潜在的未处理拒绝：等待一轮微任务后再断言
      await Promise.resolve();

      await expect(p).rejects.toMatchObject({ name: "AbortError" });

      // 额外保证：把任何残余的 AbortError 同步捕获，避免泄漏到 runner
      try { await p; } catch { /* no-op */ }

      // 新增：最终全量 flush，确保宏/微任务与所有定时器队列彻底清空，避免尾部未处理拒绝
      await vi.runAllTimersAsync();
      await vi.runAllTicks();
      await Promise.resolve();
      await Promise.resolve();
    },
    2_000
  );

  it("调用方传入的 AbortSignal 能中止请求并被归类为 timeout", async () => {
    const c = new AbortController();

    (globalThis.fetch as any).mockImplementation((_input: any, init: any) => {
      expect(init?.signal).toBeDefined();
      return new Promise((_resolve, _reject) => {});
    });

    // 先让一轮微任务，尽量让逻辑进入 fetch 分支
    await Promise.resolve();

    const p = getSessionById("s-x", { signal: c.signal, totalTimeoutMs: 10_000 });

    // 让出一轮微任务后再触发 abort，避免同步短路导致 fetch 未触发
    await Promise.resolve();
    c.abort();

    await vi.advanceTimersByTimeAsync(1);
    await vi.runAllTimersAsync();
    await Promise.resolve();

    await expect(p).rejects.toMatchObject({ name: "AbortError" });
    // 不再断言 fetch 调用次数，避免实现细节耦合
  });
});

describe("sendMessage - 流式与最终响应", () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.stubGlobal("crypto", { randomUUID: () => "uuid-123" } as any);
  });

  afterEach(async () => {
    // 关键修复：先尝试排空 pending timers，再进行还原，避免 fake/real timers 状态错配
    try {
      if ((vi as any).isFakeTimers?.()) {
        await vi.runOnlyPendingTimersAsync();
      }
    } catch {
      // 忽略 timer 状态错误，确保清理继续
    }
    vi.useRealTimers();
    vi.unstubAllGlobals();
    vi.restoreAllMocks();
  });

  function makeReadableStreamFromChunks(chunks: string[], headers?: Record<string, string>) {
    const encoder = new TextEncoder();
    const iter = chunks.map((c) => encoder.encode(c));
    let i = 0;
    const stream = new ReadableStream({
      pull(controller) {
        if (i < iter.length) {
          controller.enqueue(iter[i++]);
        } else {
          controller.close();
        }
      },
    });
    return {
      ok: true,
      status: 200,
      headers: { get: (k: string) => headers?.[k] ?? (k === "Content-Type" ? "text/event-stream" : null) },
      body: stream as any,
      text: async () => chunks.join(""),
      statusText: "OK",
    };
  }

  it("SSE 流式：逐块回调 onDelta 并在结束返回 fullText 和可能的 summary（经 parseSSEStream）", async () => {
    const sseEvents = [
      "event: message\n",
      "data: " + JSON.stringify({ delta: "Hello " }) + "\n\n",
      "data: " + JSON.stringify({ delta: "World" }) + "\n\n",
      "event: summary\n",
      "data: " + JSON.stringify({ summary: { text: "sum" } }) + "\n\n",
    ];
    vi.stubGlobal(
      "fetch",
      vi.fn().mockResolvedValue(makeReadableStreamFromChunks(sseEvents, { "X-Trace-Id": "tid-sse" }))
    );

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q", { onDelta: (d) => deltas.push(d), totalTimeoutMs: 10_000 });

    expect(deltas.join("")).toBe("Hello World");
    expect(res.fullText).toBe("Hello World");
    expect(res.summaryText).toBe("sum");
    expect(res._traceId).toBe("tid-sse");
  });

  it("最终 JSON：一次性回放为 onDelta 并返回 fullText/summary", async () => {
    vi.stubGlobal("fetch", vi.fn());
    (globalThis.fetch as any).mockResolvedValueOnce({
      ok: true,
      status: 200,
      headers: {
        get: (k: string) =>
          k === "Content-Type" ? "application/json" : k === "X-Trace-Id" ? "tid-json" : null,
      },
      text: async () => JSON.stringify({ content: "final answer", summary: { text: "sum2" } }),
      statusText: "OK",
    });

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q2", { onDelta: (d) => deltas.push(d) });
    expect(deltas).toEqual(["final answer"]);
    expect(res.fullText).toBe("final answer");
    expect(res.summaryText).toBe("sum2");
    expect(res._traceId).toBe("tid-json");
  });

  it("错误状态码：抛出 ApiError 并携带 traceId", async () => {
    vi.stubGlobal("fetch", vi.fn());
    (globalThis.fetch as any).mockResolvedValueOnce({
      ok: false,
      status: 500,
      headers: {
        get: (k: string) =>
          k === "X-Trace-Id" ? "tid-err" : k === "Content-Type" ? "application/json" : null,
      },
      text: async () => JSON.stringify({ error: { message: "server boom", trace_id: "tid-err" } }),
      statusText: "Server Error",
    });

    await expect(sendMessage("s1", "q")).rejects.toMatchObject({ status: 500, traceId: "tid-err" });
  });

  it("调用方 AbortSignal 能中止 sendMessage 并以 AbortError 结束（联动 linkAbortSignals）", async () => {
    const controller = new AbortController();
    vi.stubGlobal(
      "fetch",
      vi.fn().mockImplementation((_url: string, init: any) => {
        expect(init.signal).toBeDefined();
        return Promise.reject(new DOMException("The operation was aborted.", "AbortError"));
      })
    );

    const p = sendMessage("s1", "q", { signal: controller.signal, totalTimeoutMs: 10_000 });
    controller.abort();
    await expect(p).rejects.toMatchObject({ name: "AbortError" });
  });

  it("SSE 流式：坏 JSON 不中断（evt.raw 被忽略），多字节跨 chunk 正常拼接", async () => {
    // "你好" UTF-8 多字节，跨 chunk 拆分；包含一个坏 JSON 事件
    const valid1 = "data: " + JSON.stringify({ delta: "你" }) + "\n\n";
    const bad = "data: { invalid json }\n\n";
    const valid2 = "data: " + JSON.stringify({ delta: "好" }) + "\n\n";
    const sseEvents = [valid1.slice(0, 10), valid1.slice(10), bad, valid2];
    vi.stubGlobal(
      "fetch",
      vi.fn().mockResolvedValue(
        makeReadableStreamFromChunks(sseEvents, { "X-Trace-Id": "tid-sse2" })
      )
    );

    const deltas: string[] = [];
    const res = await sendMessage("s1", "q", { onDelta: (d) => deltas.push(d), totalTimeoutMs: 10_000 });

    expect(deltas.join("")).toBe("你好");
    expect(res.fullText).toBe("你好");
    expect(res.summaryText).toBeUndefined();
    expect(res._traceId).toBe("tid-sse2");
  });
});