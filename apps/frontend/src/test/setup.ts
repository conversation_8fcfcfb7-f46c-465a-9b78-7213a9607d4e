import "@testing-library/jest-dom/vitest";
import { vi } from "vitest";

// 统一计时器推进与微/宏任务 flush 工具，稳定超时/退避相关用例
// 使用方法：await globalThis.__advanceAndFlush__(ms, { micro: 3, macro: 1 })
declare global {
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}
if (!globalThis.__advanceAndFlush__) {
  Object.defineProperty(globalThis, "__advanceAndFlush__", {
    configurable: true,
    writable: true,
    value: async (ms: number = 0) => {
      // 默认参数：micro=3, macro=1
      const micro = 3;
      const macro = 1;
      try {
        if (vi && typeof vi.advanceTimersByTimeAsync === "function") {
          await vi.advanceTimersByTimeAsync(ms);
          if (typeof (vi as any).runOnlyPendingTimersAsync === "function") {
            await (vi as any).runOnlyPendingTimersAsync();
          }
        } else {
          await new Promise((r) => setTimeout(r, ms));
        }
      } catch {
        // 忽略推进错误，继续 flush
      }
      for (let i = 0; i < micro; i++) await Promise.resolve();
      for (let j = 0; j < macro; j++) await new Promise((r) => setTimeout(r, 0));
    },
  });
}

// Polyfill localStorage for Node test environment
if (typeof globalThis.localStorage === "undefined") {
  const store = new Map<string, string>();
  globalThis.localStorage = {
    getItem(key: string) {
      return store.has(key) ? store.get(key)! : null;
    },
    setItem(key: string, value: string) {
      store.set(key, String(value));
    },
    removeItem(key: string) {
      store.delete(key);
    },
    clear() {
      store.clear();
    },
    key(index: number) {
      return Array.from(store.keys())[index] ?? null;
    },
    get length() {
      return store.size;
    },
  } as unknown as Storage;
}

// Ensure crypto.randomUUID exists in Node test env
if (typeof globalThis.crypto === "undefined") {
  // @ts-ignore
  globalThis.crypto = {} as Crypto;
}
if (typeof (globalThis.crypto as any).randomUUID !== "function") {
  // simple fallback for tests only (type loosened for Vitest env)
  (globalThis.crypto as any).randomUUID = function randomUUID(): string {
    // RFC4122 v4 UUID string
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };
}

// jsdom not implemented APIs
if (typeof (globalThis as any).scrollTo !== "function") {
  (globalThis as any).scrollTo = () => {};
}
if (typeof window !== "undefined" && typeof window.scrollTo !== "function") {
  window.scrollTo = () => {};
}
if (typeof document.hasFocus !== "function") {
  // @ts-ignore
  document.hasFocus = () => false;
}

// Provide a safe default fetch to prevent real network calls leaking from tests that forgot to stub.
// Individual tests can override with vi.stubGlobal("fetch", ...)
if (typeof (globalThis as any).fetch !== "function") {
  (globalThis as any).fetch = async () =>
    new Response(JSON.stringify({ error: { message: "unmocked fetch" } }), {
      status: 501,
      headers: { "Content-Type": "application/json", "X-Trace-Id": "tid-unmocked" },
    });
}

/**
 * 全局 rAF polyfill：映射为 setTimeout(cb, 16)
 * - Vitest fake timers 下，通过 advanceTimersByTimeAsync 可可靠推进 rAF
 */
if (typeof globalThis.requestAnimationFrame !== "function") {
  // @ts-ignore
  globalThis.requestAnimationFrame = (cb: FrameRequestCallback) =>
    (setTimeout(() => cb(performance.now()), 16) as unknown as number);
}
if (typeof globalThis.cancelAnimationFrame !== "function") {
  // @ts-ignore
  globalThis.cancelAnimationFrame = (h: number) => clearTimeout(h as unknown as any);
}

/**
 * 标准化 fake timers 配置：
 * - 默认使用现代计时器，限定 toFake 项，避免污染微任务
 * - 每个测试可以自由切换 useFakeTimers/useRealTimers
 */
vi.useRealTimers();

declare global {
  // 统一推进工具：在测试中可调用 globalThis.__advanceAndFlush__(ms)
  // eslint-disable-next-line no-var
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

/**
 * __advanceAndFlush__（中度修复版）
 * 目标：与 React/RTL/Vitest 可靠配合，避免死循环与遗漏渲染
 * 策略：
 * 1) 分段推进宏任务（timers/raf）
 * 2) 每段后 runOnlyPendingTimersAsync
 * 3) 多轮微任务排空（Promise.resolve + setTimeout 0）
 * 4) 外层 act 包裹，确保提交渲染
 * 5) 安全上限迭代，防止挂死
 */
(async () => {
  const { vi } = await import("vitest");
  const { act } = await import("@testing-library/react");

  const MICRO_FLUSH_ROUNDS = 2;
  const STEP_MS = 50;
  const MAX_STEPS = 400; // 50ms * 400 = 20s 安全上限

  async function flushMicrotasks() {
    for (let i = 0; i < MICRO_FLUSH_ROUNDS; i++) {
      await Promise.resolve();
      await new Promise<void>((r) => setTimeout(r, 0));
    }
  }

  async function stepOnce(step: number) {
    await act(async () => {
      if (step > 0) {
        await vi.advanceTimersByTimeAsync(step);
      }
      await vi.runOnlyPendingTimersAsync();
    });
    await flushMicrotasks();
    // 再次用 act 提交潜在的状态更新
    await act(async () => {});
  }

  (globalThis as any).__advanceAndFlush__ = async (ms = 0) => {
    // 如果调用方尚未启用 fake timers，这里不强制开启，维持调用方语义
    let remaining = ms;
    let steps = 0;

    // 先做一次基础 flush，处理初始 promise/微任务
    await flushMicrotasks();
    await act(async () => {});

    while ((remaining > 0 || ms === 0) && steps < MAX_STEPS) {
      const chunk = ms === 0 ? STEP_MS : Math.min(STEP_MS, remaining);
      await stepOnce(chunk);
      if (ms > 0) remaining -= chunk;
      steps++;

      // 如果没有剩余时间并且 ms 为 0 模式（仅 flush），迭代 3 步后退出，避免无限循环
      if (ms === 0 && steps >= 3) break;
    }

    // 最终扫尾
    await act(async () => {
      await vi.runOnlyPendingTimersAsync();
    });
    await flushMicrotasks();
    await act(async () => {});
  };
})();