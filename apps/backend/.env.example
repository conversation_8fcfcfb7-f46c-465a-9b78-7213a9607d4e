# Backend .env.example (reference-only)
# 重要：为避免重复维护，环境变量以仓库根目录的 .env / .env.example 为单一事实来源（Single Source of Truth）。
# 本文件仅提供后端最小必要说明；请在根目录维护 .env，并通过 Make 目标同步生成 apps/backend/.env。
#
# 使用方法：
#   1) 在仓库根目录：
#        cp .env.example .env
#        # 按需填写 OPENAI_API_KEY / OPENAI_BASE_URL / OPENAI_MODEL 等键
#   2) 同步到后端：
#        make backend-env-sync
#      该命令会基于根 .env 生成 apps/backend/.env（仅包含后端所需键）。
#   3) 启动后端（需先完成 venv 与依赖安装）：
#        make dev
#
# 后端关键键（在根 .env 维护）：
#   - OPENAI_API_KEY       # 必填
#   - OPENAI_BASE_URL      # 必填（可为直连或网关地址，如 https://gateway.example.com/openai/v1）
#   - OPENAI_MODEL         # 必填（如 gpt-4o / gpt-4o-mini / o4-mini）
#   - OPENAI_TIMEOUT_MS    # 可选（毫秒，默认 20000）
#   - OPENAI_SDK_ENABLED   # 可选（默认 true；为 false 时回退到 legacy REST 适配器）
#   - LLM_PROVIDER         # 可选（openai-sdk | openai | auto；默认 openai-sdk）
#   - EXTERNAL_MAX_RETRIES / RETRY_BACKOFF_* / LLM_REQUEST_TIMEOUT_MS 见下
#
# 如需完全独立部署后端，可直接在 apps/backend/.env 维护同名键，但团队标准推荐通过根 .env 同步生成，避免重复编辑。
#
# ===================== LLM / OpenAI Configuration =====================
OPENAI_MODEL=gpt-4o-mini
# OpenAI 官方 SDK API key（优先）；未设置时将回退到 LLM_API_KEY（仅当 OPENAI_SDK_ENABLED=false 时生效）
OPENAI_API_KEY=
# 兼容网关的 base_url（如直连 https://api.openai.com/v1 或网关 https://gateway.example.com/openai/v1）
OPENAI_BASE_URL=
# 可选：OpenAI 组织
OPENAI_ORG=
# SDK 级超时（毫秒）；建议使用 OPENAI_TIMEOUT_MS
OPENAI_TIMEOUT_MS=20000

# Provider 选择与灰度开关
# LLM_PROVIDER: openai-sdk | openai | auto
LLM_PROVIDER=openai-sdk
# 灰度开关：false 时强制启用 legacy REST 适配器（OpenAIAdapter）
OPENAI_SDK_ENABLED=true

# Legacy 适配器的通用 fallback（当 OPENAI_SDK_ENABLED=false 时使用）
LLM_API_KEY=
LLM_API_BASE=

# 出站请求与重试策略（集中配置，对齐 1.17 AC4）
LLM_REQUEST_TIMEOUT_MS=20000
EXTERNAL_MAX_RETRIES=2
RETRY_BACKOFF_INITIAL_MS=500
RETRY_BACKOFF_MAX_MS=8000
# =====================================================================