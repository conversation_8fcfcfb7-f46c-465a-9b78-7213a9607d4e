import sys
from typing import Any, Dict, Optional
from loguru import logger

from app.core.config import settings


def setup_logging():
    """Setup structured logging with <PERSON><PERSON><PERSON>."""
    # Remove default logger
    logger.remove()

    # Configure format for structured logging
    log_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{extra[trace_id]:<36} | "
        "{message}"
    )

    # Add console handler
    logger.add(
        sys.stderr,
        format=log_format,
        level="INFO" if settings.environment == "production" else "DEBUG",
        enqueue=True,
        diagnose=settings.app_debug,
        backtrace=settings.app_debug,
    )

    # Add file handler for production
    if settings.environment == "production":
        logger.add(
            "logs/app.log",
            format=log_format,
            level="INFO",
            rotation="1 day",
            retention="30 days",
            compression="gz",
            enqueue=True,
            diagnose=False,
            backtrace=False,
        )

    logger.info("Logging setup completed", extra={"trace_id": "system"})


def get_logger_with_trace(trace_id: str = "unknown"):
    """Get logger instance with trace_id context."""
    return logger.bind(trace_id=trace_id)


def log_llm_event(
    *,
    trace_id: Optional[str],
    phase: str,
    provider: Optional[str] = None,
    model: Optional[str] = None,
    latency_ms: Optional[int] = None,
    attempts: Optional[int] = None,
    retry_policy: Optional[Dict[str, Any]] = None,
    streaming: Optional[bool] = None,
    http_status: Optional[int] = None,
    upstream_status: Optional[int] = None,
    error_type: Optional[str] = None,
    request_id: Optional[str] = None,
    extra_fields: Optional[Dict[str, Any]] = None,
    level: str = "info",
) -> None:
    """
    Emit a structured LLM event log with unified fields.

    Required:
      - trace_id: str | None
      - phase: "start" | "success" | "error" | "finish" | custom

    Optional fields mirror story AC2 requirements.
    """
    bound = get_logger_with_trace(trace_id or "unknown")

    payload: Dict[str, Any] = {
        "event": "llm_event",
        "phase": phase,
        "provider": provider,
        "model": model,
        "latency_ms": latency_ms,
        "attempts": attempts,
        "retry_policy": retry_policy,
        "streaming": streaming,
        "http_status": http_status,
        "upstream_status": upstream_status,
        "error_type": error_type,
        "request_id": request_id,
    }
    if extra_fields:
        # Avoid overwriting primary keys
        for k, v in extra_fields.items():
            if k not in payload:
                payload[k] = v

    message = " | ".join(f"{k}={v}" for k, v in payload.items() if v is not None)

    # Route by level
    if level == "debug":
        bound.debug(message)
    elif level == "warning":
        bound.warning(message)
    elif level == "error":
        bound.error(message)
    else:
        bound.info(message)