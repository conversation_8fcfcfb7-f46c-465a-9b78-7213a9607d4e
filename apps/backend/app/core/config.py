from typing import Optional, Literal
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppSettings(BaseSettings):
    """Application configuration settings."""

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False)

    # Supabase Configuration
    supabase_url: str = Field(default="https://example.supabase.co")
    supabase_anon_key: Optional[str] = Field(default=None)
    supabase_service_role_key: str = Field(default="your-service-role-key-here")

    # Database Configuration
    database_url: str = Field(default="sqlite:///:memory:")

    # Sentry Configuration (Optional)
    sentry_dsn: Optional[str] = Field(default=None)

    # LLM Configuration (simplified - OpenAI only)
    # NOTE: These defaults align with story 1.17 AC4 for centralized strategy control.
    openai_model: str = Field(default="gpt-4o-mini", description="OPENAI_MODEL")
    # Global outbound request timeout to upstream LLM (ms)
    llm_request_timeout_ms: int = Field(default=20000)
    # External retry strategy
    external_max_retries: int = Field(default=2)  # attempts after the initial one
    retry_backoff_initial_ms: int = Field(default=500)  # base delay
    retry_backoff_max_ms: int = Field(default=8000)    # cap delay

    # OpenAI SDK configuration (simplified)
    openai_api_key: Optional[str] = Field(default=None, description="OPENAI_API_KEY")
    openai_base_url: Optional[str] = Field(default=None, description="OPENAI_BASE_URL (gateway compatible)")
    openai_org: Optional[str] = Field(default=None, description="OPENAI_ORG")
    # Keep both for compatibility; prefer openai_timeout_ms
    openai_timeout: Optional[int] = Field(default=20000, description="DEPRECATED: prefer OPENAI_TIMEOUT_MS")
    openai_timeout_ms: int = Field(default=20000, description="OPENAI_TIMEOUT_MS in milliseconds")

    # LLM Provider Configuration (gray rollout)
    # llm_provider: openai-sdk | openai | auto
    llm_provider: Optional[str] = Field(default="openai-sdk")
    # Generic fallbacks to support legacy adapter when sdk disabled
    llm_api_key: Optional[str] = Field(default=None)
    llm_api_base: Optional[str] = Field(default=None)

    # Feature Flags and Caching
    # OPENAI_SDK_ENABLED=false will force legacy REST fallback
    openai_sdk_enabled: bool = Field(default=True, description="Enable OpenAI SDK adapter (gray rollout switch)")
    llm_enable_caching: bool = Field(default=False)
    llm_request_merge_window_ms: int = Field(default=0)

    # Application Configuration
    api_prefix: str = Field(default="/api")
    app_host: str = Field(default="0.0.0.0")
    app_port: int = Field(default=8000)
    app_debug: bool = Field(default=False)

    # Environment
    environment: str = Field(default="development")

    # Development Settings (Optional)
    dev_user_id: Optional[str] = Field(default="550e8400-e29b-41d4-a716-446655440000")


settings = AppSettings()