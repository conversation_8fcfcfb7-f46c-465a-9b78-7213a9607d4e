"""
LLM Integration Tests

Covers:
- Normal non-streaming response path
- Streaming response path
- Error mappings: 429 (rate limit), 5xx (server), timeout, invalid API key
- Verifies presence of unified structured logging fields (smoke-level via monkeypatch)
"""

import asyncio
import json
import os
import sys
from typing import Async<PERSON>enerator
import pytest
from unittest.mock import AsyncMock, MagicMock, patch

# Ensure 'app' package import works when pytest-cov changes working context
CURRENT_DIR = os.path.dirname(__file__)
BACKEND_ROOT = os.path.abspath(os.path.join(CURRENT_DIR, "..", ".."))
if BACKEND_ROOT not in sys.path:
    sys.path.insert(0, BACKEND_ROOT)

from app.services.llm import (
    LLMService,
    LLMMessage,
    LLMResponse,
    OpenAILibraryAdapter,
    OpenAIAdapter,
)
from app.core.errors import AppError, ErrorCode


@pytest.fixture
def messages():
    return [LLMMessage(role="user", content="Hello")]


@pytest.fixture
def set_sdk_enabled_true(monkeypatch):
    # Ensure SDK path by default
    monkeypatch.setenv("OPENAI_SDK_ENABLED", "true")
    monkeypatch.setenv("LLM_PROVIDER", "openai-sdk")
    # Minimal defaults
    monkeypatch.setenv("OPENAI_API_KEY", "test-key")
    monkeypatch.setenv("OPENAI_MODEL", "gpt-4o-mini")
    monkeypatch.setenv("EXTERNAL_MAX_RETRIES", "0")
    monkeypatch.setenv("RETRY_BACKOFF_INITIAL_MS", "1")
    monkeypatch.setenv("RETRY_BACKOFF_MAX_MS", "2")


@pytest.mark.asyncio
async def test_non_streaming_success_sdk(messages, set_sdk_enabled_true):
    """
    SDK adapter: non-streaming success should return LLMResponse with usage and metadata enriched.
    """
    service = LLMService()

    # Force adapter to SDK
    assert isinstance(service.adapter, (OpenAILibraryAdapter, type(None)))

    # Mock adapter call
    resp = LLMResponse(content="Hello there!", model="gpt-4o-mini", usage={"total_tokens": 8})
    mock_adapter = AsyncMock()
    mock_adapter.chat_completion.return_value = resp
    service.adapter = mock_adapter

    result = await service.chat_completion(messages, model="gpt-4o-mini", stream=False)
    assert isinstance(result, LLMResponse)
    assert result.content == "Hello there!"
    assert result.metadata is not None
    assert result.metadata["provider"] in ["openai-sdk", "openai"]
    assert result.metadata["model"] == "gpt-4o-mini"
    assert result.metadata["attempts"] == 1
    assert "latency_ms" in result.metadata
    assert result.metadata["streaming"] is False


@pytest.mark.asyncio
async def test_streaming_success_sdk(messages, set_sdk_enabled_true):
    """
    SDK adapter: streaming success path should iterate deltas and not raise.
    """
    service = LLMService()
    assert isinstance(service.adapter, (OpenAILibraryAdapter, type(None)))

    async def gen() -> AsyncGenerator[str, None]:
        yield "Hello"
        yield " "
        yield "world"

    mock_adapter = AsyncMock()
    mock_adapter.chat_completion.return_value = gen()
    service.adapter = mock_adapter

    chunks = []
    async for part in await service.chat_completion(messages, model="gpt-4o-mini", stream=True):  # type: ignore
        chunks.append(part)

    assert "".join(chunks) == "Hello world"


@pytest.mark.asyncio
async def test_error_429_rate_limited(messages, set_sdk_enabled_true):
    """
    429 should surface as AppError(EXTERNAL_SERVICE_ERROR) and be logged by service.
    """
    service = LLMService()
    mock_adapter = AsyncMock()
    err = AppError(
        error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
        message="rate limited",
        details={"status_code": 429},
        http_status=429,
    )
    mock_adapter.chat_completion.side_effect = err
    service.adapter = mock_adapter

    with pytest.raises(AppError) as exc:
        await service.chat_completion(messages, model="gpt-4o-mini", stream=False)

    assert exc.value.error_code == ErrorCode.EXTERNAL_SERVICE_ERROR
    assert getattr(exc.value, "http_status", None) == 429


@pytest.mark.asyncio
async def test_error_5xx_retry_exhaust(messages, set_sdk_enabled_true, monkeypatch):
    """
    5xx should be retried according to settings; with EXTERNAL_MAX_RETRIES=1 we expect 2 attempts then raise.
    Note: settings is a BaseSettings singleton; prefer patching attributes over environment variables.
    """
    # Patch settings attributes directly so LLMService reads the intended values
    with patch("app.services.llm.settings") as mock_settings:
        mock_settings.external_max_retries = 1
        mock_settings.retry_backoff_initial_ms = 1
        mock_settings.retry_backoff_max_ms = 2
        mock_settings.openai_model = "gpt-4o-mini"

        service = LLMService()
        mock_adapter = AsyncMock()
        err = AppError(
            error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
            message="upstream 502",
            details={"status_code": 502},
            http_status=502,
        )
        mock_adapter.chat_completion.side_effect = err
        service.adapter = mock_adapter

        # speed up sleep
        with patch("asyncio.sleep", new_callable=AsyncMock):
            with pytest.raises(AppError) as exc:
                await service.chat_completion(messages, model="gpt-4o-mini", stream=False)

        assert exc.value.error_code == ErrorCode.EXTERNAL_SERVICE_ERROR
        # total calls = initial + 1 retry
        assert mock_adapter.chat_completion.call_count == 2


@pytest.mark.asyncio
async def test_timeout_mapped(messages, set_sdk_enabled_true):
    """
    Simulate timeout via raising AppError(SERVICE_UNAVAILABLE) from adapter.
    """
    service = LLMService()
    mock_adapter = AsyncMock()
    err = AppError(
        error_code=ErrorCode.SERVICE_UNAVAILABLE,
        message="timeout",
        details={"reason": "timeout"},
        http_status=504,
    )
    mock_adapter.chat_completion.side_effect = err
    service.adapter = mock_adapter

    with pytest.raises(AppError) as exc:
        await service.chat_completion(messages, model="gpt-4o-mini", stream=False)
    assert exc.value.error_code == ErrorCode.SERVICE_UNAVAILABLE


@pytest.mark.asyncio
async def test_invalid_key(messages, set_sdk_enabled_true):
    """
    Invalid key path: adapter raises AppError(EXTERNAL_SERVICE_ERROR) with 401/403.
    """
    service = LLMService()
    mock_adapter = AsyncMock()
    err = AppError(
        error_code=ErrorCode.EXTERNAL_SERVICE_ERROR,
        message="unauthorized",
        details={"status_code": 401},
        http_status=401,
    )
    mock_adapter.chat_completion.side_effect = err
    service.adapter = mock_adapter

    with pytest.raises(AppError) as exc:
        await service.chat_completion(messages, model="gpt-4o-mini", stream=False)

    assert exc.value.error_code == ErrorCode.EXTERNAL_SERVICE_ERROR
    assert getattr(exc.value, "http_status", None) == 401


@pytest.mark.asyncio
async def test_legacy_fallback_initialization(monkeypatch):
    """
    When OPENAI_SDK_ENABLED=false, the service should fallback to OpenAIAdapter.
    """
    monkeypatch.setenv("OPENAI_SDK_ENABLED", "false")
    monkeypatch.setenv("LLM_PROVIDER", "openai")
    monkeypatch.setenv("OPENAI_API_KEY", "test-key")
    monkeypatch.setenv("OPENAI_MODEL", "gpt-4o-mini")

    service = LLMService()
    # If adapter init failed due to import error of SDK, fallback should be legacy
    assert isinstance(service.adapter, (OpenAIAdapter, OpenAILibraryAdapter, type(None)))


@pytest.mark.asyncio
async def test_logging_fields_smoke(messages, set_sdk_enabled_true, monkeypatch):
    """
    Smoke test to ensure log_llm_event is invoked with expected keys.
    """
    calls = []

    def fake_log_llm_event(**kwargs):
        calls.append(kwargs)

    # Patch the correct symbol where it is defined/imported from
    with patch("app.core.logging.log_llm_event", side_effect=fake_log_llm_event):
        service = LLMService()
        mock_adapter = AsyncMock()
        mock_adapter.chat_completion.return_value = LLMResponse(content="ok", model="gpt-4o-mini")
        service.adapter = mock_adapter

        await service.chat_completion(messages, model="gpt-4o-mini", stream=False)

    # Ensure at least start and success phases were logged
    phases = [c.get("phase") for c in calls]
    assert "start" in phases
    assert "success" in phases
    # Check some standard fields presence
    any_payload = calls[-1]
    assert "provider" in any_payload
    assert "model" in any_payload
    assert "attempts" in any_payload